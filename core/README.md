# **电文解析框架V1.0**

基于 Disruptor 实现的高性能解析框架

## 关键类

### @MessageHandler 注解

#### 位置

 bici.bzlj.dataprocess.core.annotation.MessageHandler

```java
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Component
public @interface MessageHandler {
    /**
     * 消息类型
     */
    String messageType();

    /**
     * 消息描述
     */
    String desc();
}

```

#### 描述

用以框架自动识别class是否是消息解析器,同时设置关键属性

### MessageEvent

#### 位置

bici.bzlj.dataprocess.core.event.MessageEvent

```java
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MessageEvent<T> {
    /**
     * 消息id
     */
    private String messageId;
    /**
     * 消息类型
     */
    private String messageType;
    /**
     * 消息内容
     */
    private T payload;
}

```

#### 描述

Disruptor 的消息事件定义

### IMessageHandler

#### 位置

bici.bzlj.dataprocess.core.handler.IMessageHandler

```java
public interface IMessageHandler<T> {
    /**
     * 消息处理
     *
     * @param event 消息事件
     */
    default void handleMessage(MessageEvent<T> event, Map<String, Object> handlesContext) {
        // 消息上下文
        Map<String, Object> currentHandleContext = Maps.newHashMap();
        try {
            // 消息处理前
            handlePre(event, currentHandleContext, handlesContext);
            // 消息处理
            handle(event, currentHandleContext, handlesContext);
            // 消息处理后
            handlePost(event, currentHandleContext, handlesContext);
        } catch (Exception e) {
            // 异常处理
            handleError(event, currentHandleContext, handlesContext, e);
        }

    }

    /**
     * 消息处理前
     *
     * @param event                消息事件
     * @param currentHandleContext 当前handle上下文
     * @param handlesContext       当前handle组上下文
     */
    void handlePre(MessageEvent<T> event, Map<String, Object> currentHandleContext, Map<String, Object> handlesContext);

    /**
     * 消息处理
     *
     * @param event                消息事件
     * @param currentHandleContext 当前handle上下文
     * @param handlesContext       当前handle组上下文
     */
    void handle(MessageEvent<T> event, Map<String, Object> currentHandleContext, Map<String, Object> handlesContext);

    /**
     * 消息处理后
     *
     * @param event                消息事件
     * @param currentHandleContext 当前handle上下文
     * @param handlesContext       当前handle组上下文
     */
    void handlePost(MessageEvent<T> event, Map<String, Object> currentHandleContext, Map<String, Object> handlesContext);

    /**
     * 错误处理
     *
     * @param event                错误消息事件
     * @param currentHandleContext 当前handle上下文
     * @param handlesContext       当前handle组上下文
     * @param e                    错误信息
     */
    void handleError(MessageEvent<T> event, Map<String, Object> currentHandleContext, Map<String, Object> handlesContext, Throwable e);


    /**
     * 获取消息描述
     *
     * @return 消息描述
     */
    default String getDesc() {
        Class<? extends IMessageHandler> clazz = this.getClass();
        if (clazz.isAnnotationPresent(MessageHandler.class)) {
            MessageHandler annotation = clazz.getAnnotation(MessageHandler.class);
            return annotation.desc();
        }
        return null;
    }

    /**
     * 消息处理顺序,默认为0,越小越先执行
     *
     * @return 消息处理顺序
     */
    default int order() {
        return 0;
    }

}
```

#### 描述

消息解析的核心接口,分别包含了 `handlePre`, `handle`, `handlePost`, `handleError`四个核心方法分别对应`消息解析前`, `消息解析`, `消息解析后`, `解析异常`四个钩子函数,同时提供了`currentHandleContext`作为当前解析器实现过程中的上下文传递,`handlesContext` 作为解析器组的上下文

#### 特别说明

如果说多个解析器的 `@MessageHandler` 注解中的 `messageType`属性一样,那么这些解析器将会被收集成为一个解析组,同时可以通过重写`order()`方法来实现解析器执行的顺序,通过`handlesContext`来传递解析组之间的上下文

### MessageEventProducer

#### 位置

bici.bzlj.dataprocess.core.producer.MessageEventProducer

```java
@RequiredArgsConstructor
@Slf4j
public class MessageEventProducer {
    private final  RingBuffer<MessageEvent> ringBuffer;

    /**
    * 发送消息
    * @param event	消息
    * <AUTHOR>
    * @date 2025/5/13 15:54
    */
    public void pushMessage(MessageEvent event){
        pushMessage(event.getMessageId(), event.getPayload());
    }

    /**
    * 发送消息
    * @param messageId	消息id
    * @param payload	消息内容
    * <AUTHOR>
    * @date 2025/5/13 15:54
    */
    public void pushMessage(String messageId, Object payload){
        long sequence = ringBuffer.next();
        try {
            MessageEvent event = ringBuffer.get(sequence);
            event.setMessageId(messageId);
            event.setPayload(payload);
        }finally {
            ringBuffer.publish(sequence);
        }
    }
}
```

#### 说明

Disruptor 事件生成者

### MessageConsumer

#### 位置

bici.bzlj.dataprocess.core.consumer.MessageConsumer

```java
public class MessageConsumer implements EventHandler<MessageEvent> {

    @Override
    public void onEvent(MessageEvent event, long sequence, boolean endOfBatch) throws Exception {
        List<IMessageHandler> handles = MessageHandlerManagement.getMessageHandlers(event.getMessageType());
        Map<String, Object> handlesContext = Maps.newHashMap();
        handlesContext.put("handleGroupSize", handles.size());
        for (int i = 0; i < handles.size(); i++) {
            handlesContext.put("currentHandleIndex", i+1);
            IMessageHandler handler = handles.get(i);
            handler.handleMessage(event, handlesContext);
        }
    }
}
```

#### 说明

Disruptor 事件消费者