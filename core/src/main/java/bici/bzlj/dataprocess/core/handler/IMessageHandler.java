package bici.bzlj.dataprocess.core.handler;

import bici.bzlj.dataprocess.core.annotation.MessageHandler;
import bici.bzlj.dataprocess.core.event.MessageEvent;
import com.google.common.collect.Maps;

import java.util.Map;

/**
 * 消息处理器接口
 * 定义消息处理的生命周期方法，包括预处理、处理、后处理和错误处理
 *
 * @param <T> 消息数据类型
 * <AUTHOR>
 * @version 1.1
 * @date 2025/5/13 16:14
 */
public interface IMessageHandler<T> {
    /**
     * 处理消息的默认实现
     * 定义了标准的消息处理流程：预处理 -> 处理 -> 后处理
     * 如果过程中发生异常，将触发错误处理
     *
     * @param event          消息事件，包含要处理的数据
     * @param handlesContext 处理器组共享的上下文，用于跨处理器传递数据
     * @throws NullPointerException 如果event参数为null
     */
    default void handleMessage(MessageEvent<T> event, Map<String, Object> handlesContext) {
        if (event == null) {
            throw new NullPointerException("MessageEvent cannot be null");
        }

        // 当前处理器的私有上下文
        Map<String, Object> currentHandleContext = Maps.newHashMap();
        try {
            // 1. 预处理阶段
            handlePre(event, currentHandleContext, handlesContext);

            // 2. 核心处理阶段
            handle(event, currentHandleContext, handlesContext);

            // 3. 后处理阶段
            handlePost(event, currentHandleContext, handlesContext);
        } catch (Exception e) {
            // 4. 异常处理阶段
            handleError(event, currentHandleContext, handlesContext, e);
        }
    }

    /**
     * 消息处理前
     *
     * @param event                消息事件
     * @param currentHandleContext 当前handle上下文
     * @param handlesContext       当前handle组上下文
     */
    void handlePre(MessageEvent<T> event,
                   Map<String, Object> currentHandleContext,
                   Map<String, Object> handlesContext);

    /**
     * 消息处理
     *
     * @param event                消息事件
     * @param currentHandleContext 当前handle上下文
     * @param handlesContext       当前handle组上下文
     */
    void handle(MessageEvent<T> event,
                Map<String, Object> currentHandleContext,
                Map<String, Object> handlesContext);

    /**
     * 消息处理后
     *
     * @param event                消息事件
     * @param currentHandleContext 当前handle上下文
     * @param handlesContext       当前handle组上下文
     */
    void handlePost(MessageEvent<T> event,
                    Map<String, Object> currentHandleContext,
                    Map<String, Object> handlesContext);

    /**
     * 错误处理
     *
     * @param event                错误消息事件
     * @param currentHandleContext 当前handle上下文
     * @param handlesContext       当前handle组上下文
     * @param e                    错误信息
     */
    void handleError(MessageEvent<T> event,
                     Map<String, Object> currentHandleContext,
                     Map<String, Object> handlesContext,
                     Exception e);

    /**
     * 获取消息描述
     *
     * @return 消息描述
     */
    default String getDesc(String messageType) {
        Class<? extends IMessageHandler> clazz = this.getClass();
        if (clazz.isAnnotationPresent(MessageHandler.class)) {
            MessageHandler annotation = clazz.getAnnotation(MessageHandler.class);
            String[] descs = annotation.desc();
            if (descs.length <= 0) {
                return "";
            }
            String[] messageTypes = annotation.messageType();
            for (int i = 0; i < messageTypes.length; i++) {
                if (messageTypes[i].equals(messageType)) {
                    return descs.length >= i + 1 ? descs[i] : "";
                }
            }
        }
        return "";
    }

    /**
     * 消息处理顺序,默认为0,越小越先执行
     *
     * @return 消息处理顺序
     */
    default int order() {
        return 0;
    }

}
