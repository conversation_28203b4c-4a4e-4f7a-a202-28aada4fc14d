package bici.bzlj.dataprocess.core.annotation;

import org.springframework.stereotype.Component;

import java.lang.annotation.*;

/**
 * 消息处理器注解
 *
 * <AUTHOR>
 * @date 2025/5/14 9:32
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Component
@Inherited
public @interface MessageHandler {
    /**
     * 消息类型
     */
    String[] messageType();

    /**
     * 消息描述
     */
    String[] desc();
}
