package bici.bzlj.dataprocess.core.consumer;

import bici.bzlj.dataprocess.core.event.MessageEvent;

import java.util.List;
import java.util.function.Consumer;

/**
 * 消息处理流程模板接口
 */
public interface ProcessTemplate {

    /**
     * 处理消息并自动管理状态
     * @param event 消息事件
     * @throws Exception 处理异常
     */
    void process(MessageEvent event) throws Exception;

    /**
     * 添加状态变更监听器
     * @param listener 状态变更监听器
     */
    void addStatusChangeListener(Consumer<StatusHistory> listener);

    /**
     * 获取当前配置的处理步骤
     * @return 处理步骤列表
     */
    List<String> getProcessingSteps();

    /**
     * 自定义处理流程配置
     * @param steps 自定义处理步骤
     */
    void configureSteps(List<String> steps);
}
