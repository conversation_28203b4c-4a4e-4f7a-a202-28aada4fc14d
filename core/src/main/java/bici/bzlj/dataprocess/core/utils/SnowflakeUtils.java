package bici.bzlj.dataprocess.core.utils;

import java.lang.management.ManagementFactory;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 雪花算法ID生成工具类
 *
 * <AUTHOR>
 * @date 2025/3/21 10:59
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
public final class SnowflakeUtils {
    /**
    * 禁止实例化
    * <AUTHOR>
    * @date 2025/5/15 15:53
    */
    private SnowflakeUtils() {
        throw new AssertionError("禁止实例化工具类");
    }

    /**
    * 单例实例
    * <AUTHOR>
    * @date 2025/5/15 15:55
    */
    private static class Holder {
        private static final Snowflake INSTANCE = new Snowflake();
    }

    /**
     * 获取分布式ID
     */
    public static long nextId() {
        return Holder.INSTANCE.nextId();
    }

    /**
     * 获取分布式ID
     */
    public static String nextStrId() {
        return nextId()+"";
    }

    /**
     * 解析ID信息
     */
    public static IdInfo parseId(long id) {
        return Holder.INSTANCE.parseId(id);
    }

    /**
     * 解析ID信息
     */
    public static IdInfo parseId(String id) {
        return parseId(Long.parseLong(id));
    }

    /**
     * 内部雪花算法实现
     */
    private static class Snowflake {
        private static final long START_TIMESTAMP = 1672531200000L;
        private static final long SEQUENCE_BIT = 12;
        private static final long WORKER_BIT = 5;
        private static final long DATA_CENTER_BIT = 5;

        private static final long MAX_SEQUENCE = ~(-1L << SEQUENCE_BIT);
        private static final long MAX_WORKER = ~(-1L << WORKER_BIT);
        private static final long MAX_DATA_CENTER = ~(-1L << DATA_CENTER_BIT);

        private final AtomicLong sequence = new AtomicLong(0);
        private final AtomicLong lastTimestamp = new AtomicLong(-1L);
        
        private final long workerId;
        private final long dataCenterId;

        Snowflake() {
            this.dataCenterId = initDataCenterId();
            this.workerId = initWorkerId(dataCenterId);
            validateConfig();
        }

        private long initDataCenterId() {
            try {
                NetworkInterface network = NetworkInterface.getByInetAddress(
                        InetAddress.getLocalHost());
                byte[] mac = network.getHardwareAddress();
                long id = ((0xFF & (long) mac[mac.length - 2]) |
                        ((0xFF & (long) mac[mac.length - 1]) << 8)) >> 6;
                return id % (MAX_DATA_CENTER + 1); // 确保不超限
            } catch (Exception e) {
                return Math.abs(new Random().nextInt()) % (MAX_DATA_CENTER + 1); // 默认值
            }
        }

        private long initWorkerId(long dataCenterId) {
            try {
                String pid = ManagementFactory.getRuntimeMXBean().getName();
                return (dataCenterId & MAX_DATA_CENTER) ^ (pid.hashCode() & MAX_WORKER);
            } catch (Exception e) {
                return Thread.currentThread().getId() % (MAX_WORKER + 1);
            }
        }

        private void validateConfig() {
            if (dataCenterId > MAX_DATA_CENTER || dataCenterId < 0) {
                throw new IllegalStateException("数据中心ID配置异常");
            }
            if (workerId > MAX_WORKER || workerId < 0) {
                throw new IllegalStateException("工作节点ID配置异常");
            }
        }

        synchronized long nextId() {
            long currentTs = System.currentTimeMillis();
            long lastTs = lastTimestamp.get();

            // 处理时钟回拨
            if (currentTs < lastTs) {
                handleClockBackwards(currentTs, lastTs);
                return nextId(); // 递归重试
            }

            // 同一毫秒内递增序列号
            if (currentTs == lastTs) {
                long seq = sequence.incrementAndGet() & MAX_SEQUENCE;
                if (seq == 0) {
                    currentTs = waitNextMillis(currentTs);
                }
                return composeId(currentTs, seq);
            }

            // 新时间周期重置序列号
            sequence.set(0);
            lastTimestamp.set(currentTs);
            return composeId(currentTs, 0);
        }

        private long composeId(long timestamp, long sequence) {
            return ((timestamp - START_TIMESTAMP) << (SEQUENCE_BIT + WORKER_BIT + DATA_CENTER_BIT))
                    | (dataCenterId << (SEQUENCE_BIT + WORKER_BIT))
                    | (workerId << SEQUENCE_BIT)
                    | sequence;
        }

        private long waitNextMillis(long currentTs) {
            long ts;
            do {
                ts = System.currentTimeMillis();
            } while (ts <= currentTs);
            return ts;
        }

        private void handleClockBackwards(long currentTs, long lastTs) {
            long offset = lastTs - currentTs;
            if (offset <= 5) {
                try {
                    wait(offset << 1);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("时钟回拨等待中断", e);
                }
            } else {
                throw new RuntimeException("检测到严重时钟回拨：" + offset + "ms");
            }
        }

        IdInfo parseId(long id) {
            return new IdInfo(
                ((id >> (SEQUENCE_BIT + WORKER_BIT + DATA_CENTER_BIT)) + START_TIMESTAMP),
                (id >> (SEQUENCE_BIT + WORKER_BIT)) & MAX_DATA_CENTER,
                (id >> SEQUENCE_BIT) & MAX_WORKER,
                id & MAX_SEQUENCE
            );
        }
    }

    /**
     * ID解析结果对象
     */
    public static record IdInfo(long timestamp, long dataCenterId, 
                              long workerId, long sequence) {
        @Override
        public String toString() {
            return String.format("时间戳: %d | 数据中心: %d | 工作节点: %d | 序列号: %d",
                    timestamp, dataCenterId, workerId, sequence);
        }
    }

    // 使用示例
//    public static void main(String[] args) {
//        for (int i = 0; i < 5; i++) {
//            long id = SnowflakeUtils.nextId();
//            System.out.println("生成ID: " + id);
//            System.out.println(SnowflakeUtils.parseId(id));
//        }
//    }
}
