package bici.bzlj.dataprocess.core.utils;

import bici.bzlj.dataprocess.core.exception.JsonException;
import com.fasterxml.jackson.core.JacksonException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.io.IOException;
import java.io.InputStream;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * JSON工具类，提供带缓存的JSON解析功能
 *  通过缓存解析结果避免重复解析相同JSON字符串，提高性能
 *
 * <AUTHOR>
 * @date 2025/3/21 10:59
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
public class JsonUtils {
    /** Jackson JSON映射器实例 */
    private static final JsonMapper JSON_MAPPER = newJsonMapper();
    
    /**
     * JSON解析结果缓存
     * - 最大容量：1000条
     * - 写入后10分钟过期
     * - 并发级别：8
     */
    private static final Cache<String, Object> JSON_CACHE = CacheBuilder.newBuilder()
        .maximumSize(1000)
        .expireAfterWrite(10, TimeUnit.MINUTES)
        .concurrencyLevel(8)
        .build();

    // 禁用构造函数
    private JsonUtils() {
        throw new AssertionError("工具类不可实例化");
    }

    /**
     * 构建自定义配置的 ObjectMapper
     */
    private static JsonMapper newJsonMapper() {
        return JsonMapper.builder()
                // 禁用自动检测 getter 方法（提升性能）
//                .disable(MapperFeature.AUTO_DETECT_GETTERS)
                // 忽略JSON中的未知属性（防止反序列化失败）
                 .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
                // 启用美化输出（开发环境有用，生产环境按需关闭）
                .enable(SerializationFeature.INDENT_OUTPUT)
                // 枚举序列化使用 toString() 而不是 name()
                .enable(SerializationFeature.WRITE_ENUMS_USING_TO_STRING)
                // 支持 Java 8 时间类型（如 LocalDateTime）
                .addModule(new JavaTimeModule())
                .build();

    }

    /**
     * 对象转 JSON 字符串
     */
    @Nullable
    public static String toJson(@Nullable Object obj) {
        return toJson(obj, false);
    }

    /**
     * 对象转 JSON 字符串（美化输出）
     */
    @Nullable
    public static String toPrettyJson(@Nullable Object obj) {
        return toJson(obj, true);
    }

    private static String toJson(@Nullable Object obj, boolean pretty) {
        if (obj == null) return null;
        try {
            return pretty ?
                    JSON_MAPPER.writerWithDefaultPrettyPrinter().writeValueAsString(obj) :
                    JSON_MAPPER.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            throw new JsonException("对象序列化失败", e);
        }
    }

    /**
     * JSON 字符串转对象（支持泛型）
     * 示例：JsonUtils.fromJson(json, new TypeReference<List<User>>() {})
     */
    /**
     * JSON字符串转对象（支持泛型，带缓存）
     * @param json JSON字符串（非空且非空白）
     * @param typeRef 目标类型引用
     * @return 解析后的对象
     * @throws JsonException 当JSON格式错误或解析失败时抛出
     * @throws IllegalArgumentException 当输入参数无效时抛出
     */
    @NonNull
    public static <T> T fromJson(@NonNull String json, @NonNull TypeReference<T> typeRef) {
        if (json == null || json.isBlank()) {
            throw new IllegalArgumentException("JSON字符串不能为空");
        }
        
        // 改进的缓存键：类型+JSON哈希值+前100字符（避免长字符串占用内存）
        String jsonHash = Integer.toHexString(json.hashCode());
        String jsonPrefix = json.length() > 100 ? json.substring(0, 100) : json;
        String cacheKey = String.format("typeRef:%s:%s:%s", 
            typeRef.getType().getTypeName(), jsonHash, jsonPrefix);
            
        try {
            return (T) JSON_CACHE.get(cacheKey, () -> {
                try {
                    return JSON_MAPPER.readValue(json, typeRef);
                } catch (JacksonException e) {
                    // 记录原始异常详细信息
                    throw new JsonException(String.format("JSON解析失败: %s (at %s)", 
                        e.getMessage(), jsonPrefix), e);
                }
            });
        } catch (Exception e) {
            if (e.getCause() instanceof JsonException) {
                throw (JsonException) e.getCause(); // 直接抛出原始解析异常
            }
            throw new JsonException("缓存操作失败: " + e.getMessage(), e);
        }
    }

    /**
     * JSON字符串转对象（带缓存）
     * @param json JSON字符串（非空且非空白）
     * @param clazz 目标类类型
     * @return 解析后的对象
     * @throws JsonException 当JSON格式错误或解析失败时抛出
     * @throws IllegalArgumentException 当输入参数无效时抛出
     */
    @NonNull
    public static <T> T fromJson(@NonNull String json, @NonNull Class<T> clazz) {
        if (json == null || json.isBlank()) {
            throw new IllegalArgumentException("JSON字符串不能为空");
        }
        
        // 改进的缓存键：类名+JSON哈希值+前100字符
        String jsonHash = Integer.toHexString(json.hashCode());
        String jsonPrefix = json.length() > 100 ? json.substring(0, 100) : json;
        String cacheKey = String.format("class:%s:%s:%s", 
            clazz.getName(), jsonHash, jsonPrefix);
            
        try {
            return (T) JSON_CACHE.get(cacheKey, () -> {
                try {
                    return JSON_MAPPER.readValue(json, clazz);
                } catch (JacksonException e) {
                    throw new JsonException(String.format("JSON解析失败: %s (at %s)", 
                        e.getMessage(), jsonPrefix), e);
                }
            });
        } catch (Exception e) {
            if (e.getCause() instanceof JsonException) {
                throw (JsonException) e.getCause();
            }
            throw new JsonException("缓存操作失败: " + e.getMessage(), e);
        }
    }

    /**
     * 安全解析 JSON 字符串（返回Optional）
     */
    @NonNull
    public static <T> Optional<T> safeParse(@Nullable String json, @NonNull Class<T> clazz) {
        if (json == null || json.isBlank()) return Optional.empty();
        try {
            return Optional.of(JSON_MAPPER.readValue(json, clazz));
        } catch (JacksonException e) {
            return Optional.empty();
        }
    }

    /**
     * 流式解析（适合大文件处理）
     */
    @NonNull
    public static <T> T parseStream(@NonNull InputStream input, @NonNull Class<T> clazz) {
        try {
            return JSON_MAPPER.readValue(input, clazz);
        } catch (IOException e) {
            throw new JsonException("流式解析失败", e);
        }
    }

    /**
     * 转换为 JsonNode
     */
    @NonNull
    public static JsonNode toJsonNode(@NonNull String json) {
        try {
            return JSON_MAPPER.readTree(json);
        } catch (JacksonException e) {
            throw new JsonException("JSON转换失败", e);
        }
    }

    /**
     * 获取 JSON 节点值
     * 示例：JsonUtils.getNodeValue(json, "/user/name", String.class)
     */
    @Nullable
    public static <T> T getNodeValue(@NonNull String json, @NonNull String path, @NonNull Class<T> type) {
        try {
            JsonNode root = JSON_MAPPER.readTree(json);
            JsonNode node = root.at(path);
            return JSON_MAPPER.treeToValue(node, type);
        } catch (JacksonException e) {
            return null;
        }
    }

    /**
     * 对象深拷贝
     */
    @SuppressWarnings("unchecked")
    @NonNull
    public static <T> T deepClone(@NonNull T obj) {
        return fromJson(toJson(obj), (Class<T>) obj.getClass());
    }

    /**
     * 对象转 Map（保留类型信息）
     */
    @NonNull
    public static Map<String, Object> toMap(@NonNull Object obj) {
        try {
            // 验证对象类型，确保是可序列化的对象
            if (obj.getClass().isPrimitive() || obj instanceof Number || obj instanceof Boolean || obj instanceof String) {
                throw new IllegalArgumentException("基本类型或简单对象不能直接转换为Map");
            }
            return JSON_MAPPER.convertValue(obj, new TypeReference<Map<String, Object>>() {});
        } catch (IllegalArgumentException e) {
            throw new JsonException("对象转换失败: " + e.getMessage(), e);
        }
    }

}
