package bici.bzlj.dataprocess.core.producer;

import bici.bzlj.dataprocess.core.event.MessageEvent;
import bici.bzlj.dataprocess.core.model.DataFlowStatus;
import com.lmax.disruptor.RingBuffer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * 消息事件生产者
 *
 * <AUTHOR>
 * @date 2025/5/13 15:39
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@RequiredArgsConstructor
@Slf4j
public class MessageEventProducer {
    private final RingBuffer<MessageEvent<?>> ringBuffer;

    /**
     * 发送消息
     *
     * @param event 消息
     * <AUTHOR>
     * @date 2025/5/13 15:54
     */
    public void pushMessage(MessageEvent<?> event) {
        pushMessage(event.getMessageId(),event.getMessageType(), event.getPayload(),event.getConvertData(),event.getDataFlowStatus());
    }

    /**
     * 发送消息
     *
     * @param messageId 消息id
     * @param payload   消息内容
     * <AUTHOR>
     * @date 2025/5/13 15:54
     */
    public void pushMessage(String messageId, String messageType, Object payload, String convertData, DataFlowStatus dataFlowStatus) {
        long sequence = ringBuffer.next();
        try {
            MessageEvent event = ringBuffer.get(sequence);
            event.setMessageId(messageId);
            event.setPayload(payload);
            event.setConvertData(convertData);
            event.setMessageType(messageType);
            event.setDataFlowStatus(dataFlowStatus);
        } finally {
            ringBuffer.publish(sequence);
        }
    }
}
