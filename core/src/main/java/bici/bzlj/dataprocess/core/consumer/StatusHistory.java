package bici.bzlj.dataprocess.core.consumer;

import bici.bzlj.dataprocess.core.model.EventStatus;

import java.time.LocalDateTime;

/**
 * 消息状态变更历史记录
 */
public class StatusHistory {
    private final EventStatus previousStatus;
    private final EventStatus newStatus;
    private final LocalDateTime changeTime;
    private final String reason;

    public StatusHistory(EventStatus previousStatus,
                       EventStatus newStatus,
                       String reason) {
        this.previousStatus = previousStatus;
        this.newStatus = newStatus;
        this.changeTime = LocalDateTime.now();
        this.reason = reason;
    }

    // Getters
    public EventStatus getPreviousStatus() {
        return previousStatus;
    }

    public EventStatus getNewStatus() {
        return newStatus;
    }

    public LocalDateTime getChangeTime() {
        return changeTime;
    }

    public String getReason() {
        return reason;
    }

    @Override
    public String toString() {
        return String.format("[%s] %s → %s: %s",
                changeTime, previousStatus, newStatus, reason);
    }
}
