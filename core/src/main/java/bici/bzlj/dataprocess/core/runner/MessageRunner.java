package bici.bzlj.dataprocess.core.runner;

import com.lmax.disruptor.dsl.Disruptor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 数据处理引擎启动类
 * 负责启动和管理Disruptor环形缓冲区
 * 包含缓冲区使用率监控和告警功能
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/14 10:54
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class MessageRunner implements ApplicationRunner {
    private final Disruptor<?> disruptor;

    /**
     * 缓冲区监控线程池
     */
    private ScheduledExecutorService monitorExecutor;

    /**
     * 缓冲区使用率告警阈值(百分比)
     */
    private static final int ALERT_THRESHOLD = 80;

    /**
     * 监控任务执行间隔(秒)
     */
    private static final int MONITOR_INTERVAL = 5;

    /**
     * 启动数据处理引擎
     *
     * @param args 应用启动参数
     * @throws Exception 当启动失败时抛出异常
     */
    @Override
    public void run(ApplicationArguments args) throws Exception {
        try {
            // 参数校验
            if (disruptor == null) {
                throw new IllegalStateException("Disruptor实例未正确初始化");
            }

            // 启动Disruptor
            disruptor.start();
            log.info("数据处理引擎启动成功");
            log.info("Disruptor配置: {}", disruptor);
            log.info("环形缓冲区大小: {}", disruptor.getRingBuffer().getBufferSize());

            /*
             * 启动缓冲区监控任务
             * 定时检查环形缓冲区使用率，超过阈值时发出告警
             */
            monitorExecutor = Executors.newSingleThreadScheduledExecutor();
            monitorExecutor.scheduleAtFixedRate(() -> {
                try {
                    // 计算缓冲区使用率
                    long bufferSize = disruptor.getRingBuffer().getBufferSize();
                    long remainingCapacity = disruptor.getRingBuffer().remainingCapacity();
                    long used = bufferSize - remainingCapacity;
                    double usageRate = (double) used / bufferSize * 100;

                    // 使用率超过阈值时记录告警日志
                    if (usageRate >= ALERT_THRESHOLD) {
                        log.warn("环形缓冲区使用率过高: {}/{} ({}%) - 已达到告警阈值{}%",
                                used, bufferSize, String.format("%.2f", usageRate), ALERT_THRESHOLD);
                    } else {
                        log.debug("环形缓冲区使用率: {}/{} ({}%)",
                                used, bufferSize, String.format("%.2f", usageRate));
                    }
                } catch (Exception e) {
                    log.error("缓冲区监控异常", e);
                }
            }, MONITOR_INTERVAL, MONITOR_INTERVAL, TimeUnit.SECONDS);

            /*
             * 注册JVM关闭钩子
             * 用于在应用关闭时优雅地停止Disruptor和监控线程
             */
            Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                log.info("正在关闭数据处理引擎...");
                // 关闭Disruptor
                disruptor.shutdown();
                // 关闭监控线程池
                if (monitorExecutor != null) {
                    monitorExecutor.shutdown();
                    try {
                        // 等待5秒让监控任务完成
                        if (!monitorExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                            // 超时后强制关闭
                            monitorExecutor.shutdownNow();
                        }
                    } catch (InterruptedException e) {
                        // 中断异常处理
                        monitorExecutor.shutdownNow();
                        Thread.currentThread().interrupt();
                    }
                }
                log.info("数据处理引擎已安全关闭");
            }));
        } catch (Exception e) {
            log.error("数据处理引擎启动失败", e);
            throw e;
        }
    }
}
