package bici.bzlj.dataprocess.core.consumer;

import bici.bzlj.dataprocess.core.event.MessageEvent;
import bici.bzlj.dataprocess.core.metrics.ConsumerMetricsUtil;
import com.lmax.disruptor.BatchEventProcessor;
import com.lmax.disruptor.EventHandler;
import com.lmax.disruptor.RingBuffer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
 * 消息事件消费者实现
 * 负责从Disruptor环形缓冲区消费消息事件并分发给对应的处理器
 *
 * <AUTHOR>
 * @version 1.1
 * @date 2025/5/13 16:00
 */
@Slf4j
public class MessageConsumer implements EventHandler<MessageEvent<?>> {
    private final ProcessTemplate processTemplate;

    public MessageConsumer() {
        this.processTemplate = new DefaultProcessTemplate();
        // 添加状态变更监听
        this.processTemplate.addStatusChangeListener(this::logStatusChange);
    }

    private void logStatusChange(StatusHistory history) {
        log.info("消息状态变更: {}", history);
    }


    /**
     * Called when a publisher has published an event to the {@link RingBuffer}.  The {@link BatchEventProcessor} will
     * read messages from the {@link RingBuffer} in batches, where a batch is all of the events available to be
     * processed without having to wait for any new event to arrive.  This can be useful for event handlers that need
     * to do slower operations like I/O as they can group together the data from multiple events into a single
     * operation.  Implementations should ensure that the operation is always performed when endOfBatch is true as
     * the time between that message and the next one is indeterminate.
     *
     * @param event      published to the {@link RingBuffer}
     * @param sequence   of the event being processed
     * @param endOfBatch flag to indicate if this is the last event in a batch from the {@link RingBuffer}
     * @throws Exception if the EventHandler would like the exception handled further up the chain.
     */
    @Override
    public void onEvent(MessageEvent<?> event, long sequence, boolean endOfBatch) throws Exception {
        long startTime = System.currentTimeMillis(); // 记录处理开始时间
        try {
            // 使用流程模板处理消息
            processTemplate.process(event);
            // 计算本次处理耗时
            long processTime = System.currentTimeMillis() - startTime;
            // 更新处理指标
            updateMetrics(processTime, true);

            if (log.isDebugEnabled()) {
                log.debug("处理消息成功, ID: {}, 耗时: {}ms", event.getMessageId(), processTime);
            }
        } catch (Exception e) {
            long processTime = System.currentTimeMillis() - startTime;
            updateMetrics(processTime, false);
            log.error("处理消息失败, ID: {}, 耗时: {}ms, 错误: {}",
                    event.getMessageId(), processTime, e.getMessage(), e);
            throw e;
        }
    }

    // 
    private void updateMetrics(long processTime, boolean success) {
        ConsumerMetricsUtil.updateMetrics(processTime, success);
    }
}
