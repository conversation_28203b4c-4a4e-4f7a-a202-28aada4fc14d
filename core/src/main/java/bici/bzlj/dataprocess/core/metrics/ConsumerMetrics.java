package bici.bzlj.dataprocess.core.metrics;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2025/5/28 15:01
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ConsumerMetrics {
    private Long totalProcessed;
    private Long totalFailed;
    private Long maxProcessTime;
    private Long minProcessTime;
    private Long totalProcessTime;
}
