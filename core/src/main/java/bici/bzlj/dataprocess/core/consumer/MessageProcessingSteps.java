package bici.bzlj.dataprocess.core.consumer;

import bici.bzlj.dataprocess.core.event.MessageEvent;
import bici.bzlj.dataprocess.core.handler.IMessageHandler;
import bici.bzlj.dataprocess.core.handler.MessageHandlerManagement;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;

/**
 * 消息处理步骤实现
 */
public class MessageProcessingSteps {

    public static void executeMainProcess(MessageEvent<?> event) {
        List<IMessageHandler<?>> handles = MessageHandlerManagement.getMessageHandlers(event.getMessageType());
        Map<String, Object> handlesContext = Maps.newHashMap();
        handlesContext.put("handleGroupSize", handles.size());

        for (int i = 0; i < handles.size(); i++) {
            handlesContext.put("currentHandleIndex", i + 1);
            IMessageHandler handler = handles.get(i);
            handler.handleMessage(event, handlesContext);
        }
    }
}
