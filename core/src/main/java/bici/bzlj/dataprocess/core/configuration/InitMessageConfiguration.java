package bici.bzlj.dataprocess.core.configuration;

import bici.bzlj.dataprocess.core.consumer.MessageConsumer;
import bici.bzlj.dataprocess.core.event.MessageEvent;
import bici.bzlj.dataprocess.core.processer.MessageHandlerPostProcessor;
import bici.bzlj.dataprocess.core.producer.MessageEventProducer;
import bici.bzlj.dataprocess.core.runner.MessageRunner;
import com.lmax.disruptor.*;
import com.lmax.disruptor.dsl.Disruptor;
import com.lmax.disruptor.dsl.ProducerType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 消息处理核心配置类
 * 配置Disruptor高性能队列及其相关组件
 *
 * <AUTHOR>
 * @version 1.1
 * @date 2025/5/13 16:48
 */
@Configuration
@Slf4j
@Import({MessageHandlerPostProcessor.class, MessageRunner.class})
public class InitMessageConfiguration {

    @Value("${disruptor.buffer.size:1048576}") // 默认1MB
    private int bufferSize;

    @Value("${disruptor.thread.max:4}") 
    private int maxThreads;
    
    @Value("${disruptor.wait.strategy:YIELDING}")
    private String waitStrategy;

    /**
     * 增强版线程工厂
     * 1. 限制最大线程数
     * 2. 添加线程异常处理器
     * 3. 更完善的线程配置
     */
    static class DisruptorThreadFactory implements ThreadFactory {
        private final AtomicInteger count = new AtomicInteger(0);
        private final int maxThreads;
        
        DisruptorThreadFactory(int maxThreads) {
            this.maxThreads = maxThreads;
        }
    
        @Override
        public Thread newThread(Runnable r) {
            if (count.get() >= maxThreads) {
                throw new IllegalStateException("Max disruptor threads reached: " + maxThreads);
            }
            
            Thread thread = new Thread(r);
            thread.setName("Disruptor-Worker-" + count.getAndIncrement());
            thread.setDaemon(true);
            thread.setPriority(Thread.NORM_PRIORITY);
            thread.setUncaughtExceptionHandler((t, e) -> 
                log.error("Disruptor worker thread {} terminated unexpectedly", t.getName(), e));
            return thread;
        }
    }

    private Disruptor<MessageEvent<?>> disruptor;

    /**
     * 创建并配置Disruptor实例
     * 1. 添加缓冲区大小校验
     * 2. 支持可配置的等待策略
     * 3. 更完善的异常处理
     */
    @Bean 
    public Disruptor<MessageEvent<?>> getDisruptor() {
        // 校验缓冲区大小
        if (bufferSize < 1024 || bufferSize > 16777216) {
            throw new IllegalArgumentException("Buffer size must be between 1K and 16M");
        }
        
        try {
            // 可配置的等待策略
            WaitStrategy strategy = switch (waitStrategy) {
                case "BLOCKING" -> new BlockingWaitStrategy();
                case "SLEEPING" -> new SleepingWaitStrategy();
                case "BUSY_SPIN" -> new BusySpinWaitStrategy();
                default -> new YieldingWaitStrategy();
            };
            
            disruptor = new Disruptor<>(
                MessageEvent::new,
                bufferSize,
                new DisruptorThreadFactory(maxThreads),
                ProducerType.MULTI,
                strategy
            );
            
            disruptor.handleEventsWith(new MessageConsumer());
            log.info("Disruptor started with bufferSize={}, threads={}, strategy={}", 
                bufferSize, maxThreads, waitStrategy);
            return disruptor;
        } catch (IllegalArgumentException e) {
            log.error("Invalid Disruptor configuration", e);
            throw new IllegalStateException("Invalid Disruptor config: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("Disruptor initialization failed", e);
            throw new IllegalStateException("Disruptor init failed", e);
        }
    }

    /**
     * 创建消息事件生产者
     *
     * @param disruptor 已初始化的Disruptor实例
     * @return 消息事件生产者
     */
    @Bean
    public MessageEventProducer getMessageEventProducer(Disruptor<MessageEvent<?>> disruptor) {
        return new MessageEventProducer(disruptor.getRingBuffer());
    }
}
