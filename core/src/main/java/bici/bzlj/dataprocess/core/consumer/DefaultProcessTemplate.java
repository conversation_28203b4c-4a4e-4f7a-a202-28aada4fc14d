package bici.bzlj.dataprocess.core.consumer;

import bici.bzlj.dataprocess.core.event.MessageEvent;
import bici.bzlj.dataprocess.core.model.EventStatus;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.function.Consumer;

/**
 * 默认消息处理流程模板实现
 */
public class DefaultProcessTemplate implements ProcessTemplate {

    private final List<String> processingSteps = new ArrayList<>();
    private final List<Consumer<StatusHistory>> statusChangeListeners = new CopyOnWriteArrayList<>();

    public DefaultProcessTemplate() {
        // 默认处理步骤
        processingSteps.add("pre-process");
        processingSteps.add("main-process");
        processingSteps.add("post-process");
    }

    @Override
    public void process(MessageEvent event) throws Exception {
        try {
            // 初始状态设置
            updateStatus(event, null, EventStatus.PROCESSING, "开始处理消息");

            // 执行处理流程
            for (String step : processingSteps) {
                executeStep(event, step);
            }

            // 处理成功
            updateStatus(event, EventStatus.PROCESSING,
                       EventStatus.COMPLETED, "消息处理完成");
        } catch (Exception e) {
            // 处理失败
            updateStatus(event, EventStatus.PROCESSING,
                       EventStatus.FAILED, "处理失败: " + e.getMessage());
            throw e;
        }
    }

    private void executeStep(MessageEvent event, String step) throws Exception {
        switch (step) {
            case "pre-process":
                // 框架预留前置处理逻辑
                break;
            case "main-process":
                MessageProcessingSteps.executeMainProcess(event);
                break;
            case "post-process":
                // 框架预留后置处理逻辑
                break;
            default:
                throw new IllegalArgumentException("未知的处理步骤: " + step);
        }
    }

    private void updateStatus(MessageEvent event, 
                            EventStatus oldStatus,
                            EventStatus newStatus,
                            String reason) {
        // 更新事件状态
        event.setStatus(newStatus);

        // 记录状态变更
        StatusHistory history = new StatusHistory(oldStatus, newStatus, reason);

        // 通知监听器
        statusChangeListeners.forEach(listener -> listener.accept(history));
    }

    @Override
    public void addStatusChangeListener(Consumer<StatusHistory> listener) {
        statusChangeListeners.add(listener);
    }

    @Override
    public List<String> getProcessingSteps() {
        return new ArrayList<>(processingSteps);
    }

    @Override
    public void configureSteps(List<String> steps) {
        processingSteps.clear();
        processingSteps.addAll(steps);
    }
}
