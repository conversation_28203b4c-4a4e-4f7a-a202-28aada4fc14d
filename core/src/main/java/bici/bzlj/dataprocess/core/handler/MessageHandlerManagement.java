package bici.bzlj.dataprocess.core.handler;

import com.google.common.collect.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 消息处理器管理中心
 * 负责管理所有消息处理器的注册和查找，支持按消息类型分类和优先级排序
 *
 * <AUTHOR>
 * @version 1.1
 * @date 2025/5/14 9:30
 */
public class MessageHandlerManagement {
    /**
     * 消息处理器池
     * key: 消息类型
     * value: 该消息类型对应的处理器列表(已按优先级排序)
     */
    private static final Map<String, List<IMessageHandler<?>>> MESSAGE_HANDLER_POOL = new ConcurrentHashMap<>();

    /**
     * 注册消息处理器
     *
     * @param messageType    消息类型，不能为空
     * @param messageHandler 消息处理器实例，不能为空
     * @throws IllegalArgumentException 如果参数为空
     */
    public static void register(String messageType, IMessageHandler<?> messageHandler) {
        if (StringUtils.isBlank(messageType)) {
            throw new IllegalArgumentException("消息类型不能为空");
        }
        if (messageHandler == null) {
            throw new IllegalArgumentException("消息处理器不能为空");
        }

        // 使用computeIfAbsent保证线程安全
        List<IMessageHandler<?>> messageHandlers = MESSAGE_HANDLER_POOL
                .computeIfAbsent(messageType, k -> Lists.newArrayList());

        // 添加处理器并排序
        messageHandlers.add(messageHandler);
        if (messageHandlers.size() > 1) {
            messageHandlers.sort(Comparator.comparingInt(IMessageHandler::order));
        }
    }

    /**
     * 获取指定消息类型的处理器列表
     *
     * @param messageType 消息类型
     * @return 该消息类型的处理器列表(已按优先级排序)，如果不存在则返回空列表
     */
    public static List<IMessageHandler<?>> getMessageHandlers(String messageType) {
        List<IMessageHandler<?>> messageHandlers = MESSAGE_HANDLER_POOL.get(messageType);
        return ObjectUtils.isEmpty(messageHandlers) ? Lists.newArrayList() : messageHandlers;
    }
}
