package bici.bzlj.dataprocess.core.metrics;

import java.util.concurrent.atomic.AtomicLong;

public class ConsumerMetricsUtil {
    private static final AtomicLong TOTAL_PROCESSED = new AtomicLong(0);
    private static final AtomicLong TOTAL_FAILED = new AtomicLong(0);
    private static final AtomicLong MAX_PROCESS_TIME = new AtomicLong(0);
    private static final AtomicLong MIN_PROCESS_TIME = new AtomicLong(Long.MAX_VALUE);
    private static final AtomicLong TOTAL_PROCESS_TIME = new AtomicLong(0);

    // 静态方法获取指标
    public static long getTotalProcessed() {
        return TOTAL_PROCESSED.get();
    }

    public static long getTotalFailed() {
        return TOTAL_FAILED.get();
    }

    public static long getMaxProcessTime() {
        return MAX_PROCESS_TIME.get();
    }

    public static long getMinProcessTime() {
        return MIN_PROCESS_TIME.get();
    }

    public static long getTotalProcessTime() {
        return TOTAL_PROCESS_TIME.get();
    }


    public static ConsumerMetrics getMetrics() {
        return new ConsumerMetrics(TOTAL_PROCESSED.get(),
                TOTAL_FAILED.get(),
                MAX_PROCESS_TIME.get(),
                MIN_PROCESS_TIME.get(),
                TOTAL_PROCESS_TIME.get());
    }


    public static double getAvgProcessTime() {
        long processed = TOTAL_PROCESSED.get() + TOTAL_FAILED.get();
        return processed > 0 ? (double) TOTAL_PROCESS_TIME.get() / processed : 0;
    }

    // 静态方法更新指标
    public static void updateMetrics(long processTime, boolean success) {
        if (success) {
            TOTAL_PROCESSED.incrementAndGet();
        } else {
            TOTAL_FAILED.incrementAndGet();
        }

        MAX_PROCESS_TIME.updateAndGet(current -> Math.max(current, processTime));
        MIN_PROCESS_TIME.updateAndGet(current -> Math.min(current, processTime));
        TOTAL_PROCESS_TIME.addAndGet(processTime);
    }

    // 静态方法重置指标
    public static void reset() {
        TOTAL_PROCESSED.set(0);
        TOTAL_FAILED.set(0);
        MAX_PROCESS_TIME.set(0);
        MIN_PROCESS_TIME.set(Long.MAX_VALUE);
        TOTAL_PROCESS_TIME.set(0);
    }
}
