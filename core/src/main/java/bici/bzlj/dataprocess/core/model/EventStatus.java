package bici.bzlj.dataprocess.core.model;

/**
 * 事件状态枚举
 */
public enum EventStatus {
    /**
     * 待处理状态
     */
    PENDING,

    /**
     * 处理中状态
     */
    PROCESSING,

    /**
     * 处理完成状态
     */
    COMPLETED,

    /**
     * 处理失败状态
     */
    FAILED;

    /**
     * 检查是否是终态（COMPLETED或FAILED）
     */
    public boolean isFinalState() {
        return this == COMPLETED || this == FAILED;
    }
}
