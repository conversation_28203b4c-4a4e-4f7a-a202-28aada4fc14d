package bici.bzlj.dataprocess.core.event;

import bici.bzlj.dataprocess.core.model.DataFlowStatus;
import bici.bzlj.dataprocess.core.model.EventStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.time.Instant;
import java.util.List;
import java.util.Map;

/**
 * 消息事件封装类
 * 表示系统中传递的消息事件，包含消息元数据和实际负载
 *
 * @param <T> 消息负载类型，必须实现Serializable接口
 * <AUTHOR>
 * @version 1.1
 * @date 2025/5/13 15:29
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MessageEvent<T> implements Serializable {
    /**
     * 消息唯一标识
     * 格式要求：非空且长度大于0
     */
    private String messageId;

    /**
     * 消息类型标识
     * 格式要求：非空且长度大于0
     */
    private String messageType;

    /**
     * 事件创建时间戳(UTC)
     */
    private Instant createdAt = Instant.now();

    /**
     * 消息处理状态
     */
    private EventStatus status = EventStatus.PENDING;

    /**
     * 消息负载数据
     * 要求：必须实现Serializable接口
     */
    private T payload;

    /**
     * 转化后的数据
     */
    private String convertData;

    /**
     * 数据流转状态
     */
    private DataFlowStatus dataFlowStatus;

    /**
     * 验证消息事件的有效性
     *
     * @throws IllegalStateException 如果消息不合法
     */
    public void validate() {
        if (StringUtils.isBlank(messageId)) {
            throw new IllegalStateException("消息ID不能为空");
        }
        if (StringUtils.isBlank(messageType)) {
            throw new IllegalStateException("消息类型不能为空");
        }
        if (payload == null) {
            throw new IllegalStateException("消息负载不能为空");
        }
    }


}
