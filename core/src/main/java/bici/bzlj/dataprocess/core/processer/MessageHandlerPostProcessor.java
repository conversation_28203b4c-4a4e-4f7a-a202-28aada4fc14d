package bici.bzlj.dataprocess.core.processer;

import bici.bzlj.dataprocess.core.annotation.MessageHandler;
import bici.bzlj.dataprocess.core.handler.IMessageHandler;
import bici.bzlj.dataprocess.core.handler.MessageHandlerManagement;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.stereotype.Component;

/**
 * 消息处理器后置处理器
 * 负责扫描并注册所有带有@MessageHandler注解的消息处理器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/14 9:11
 */
@Component
@Slf4j
public class MessageHandlerPostProcessor implements BeanPostProcessor {

    /**
     * Bean初始化后处理逻辑
     *
     * @param bean     当前处理的Bean实例
     * @param beanName Bean名称
     * @return 处理后的Bean实例
     * @throws BeansException 当处理过程中发生错误时抛出
     */
    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        try {
            Class<?> clazz = bean.getClass();
            if (clazz.isAnnotationPresent(MessageHandler.class) && bean instanceof IMessageHandler<?> handler) {
                MessageHandler annotation = clazz.getAnnotation(MessageHandler.class);
                String[] messageTypes = annotation.messageType();
                String[] descs = annotation.desc();
                for (int i = 0; i < messageTypes.length; i++) {
                    String messageType = messageTypes[i];
                    String desc = descs.length >= i + 1 ? descs[i] : "";
                    // 注册消息处理器
                    MessageHandlerManagement.register(messageType, handler);
                    log.info("注册消息处理器成功 ===> 消息类型: {}, 处理器类: {},描述: {}",
                            messageType, clazz.getName(), desc);
                    log.debug("处理器详细信息: {}", handler.getClass().getSimpleName());
                }
            }
            return bean;
        } catch (Exception e) {
            log.error("注册消息处理器失败, beanName: {}", beanName, e);
            throw new BeansException("注册消息处理器失败: " + beanName, e) {
            };
        }
    }
}
