<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>bici.bzlj</groupId>
        <artifactId>data-process-engine</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>decode</artifactId>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <scope>runtime</scope>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-stream-kafka</artifactId>
            <version>4.2.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>org.testng</groupId>
            <artifactId>testng</artifactId>
        </dependency>
        <dependency>
            <groupId>bici.bzlj</groupId>
            <artifactId>core</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!-- Caffeine Cache for performance optimization -->
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
            <version>3.1.8</version>
        </dependency>
        <!-- excel工具 -->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>jakarta.servlet</groupId>
            <artifactId>jakarta.servlet-api</artifactId>
            <version>6.0.0</version>
        </dependency>

        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.17.0</version>
        </dependency>

        <dependency>
            <groupId>com.bzlj</groupId>
            <artifactId>dynamic-mongo-spring-boot-starter</artifactId>
            <version>${dynamic.mongo.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.0.0</version>
                <configuration>
                    <skip>false</skip>
                    <testFailureIgnore>true</testFailureIgnore>
                    <suiteXmlFiles>
                        <suiteXmlFile>src/test/resources/testng.xml</suiteXmlFile>
                    </suiteXmlFiles>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <finalName>DecodeApplication</finalName>
                    <layout>ZIP</layout>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>pl.project13.maven</groupId>
                <artifactId>git-commit-id-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>revision</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.google.cloud.tools</groupId>
                <artifactId>jib-maven-plugin</artifactId>
                <version>3.4.4</version>
                <executions>
                    <execution>
                        <phase>${jib.phase.backed}</phase>
                        <goals>
                            <goal>build</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <!-- 拉取所需的基础镜像 - 这里用于运行springboot项目 -->
                    <from>
                        <image>***************/public/alpine-openjdk21-jre:latest</image>
                        <auth>
                            <username>admin</username>
                            <password>Changeme_123</password>
                        </auth>
                    </from>

                    <!-- 最后生成的镜像配置 -->
                    <to>
                        <!--                        <image>${harbor.host}/${harbor.namespace}-${docker.repository}/${artifactId}</image>-->
                        <image>${harbor.host}/${harbor.namespace}/bwty-${project.artifactId}</image>
                        <!-- 镜像版本号 -->
                        <tags>
                            <!--                            <tag>${git.branch}</tag>-->
                            <tag>${maven.build.timestamp}</tag>
                        </tags>
                        <auth>
                            <username>${harbor.username}</username>
                            <password>${harbor.password}</password>
                        </auth>
                    </to>

                    <container>
                        <!--镜像创建时间改成为当前时间-->
                        <creationTime>USE_CURRENT_TIMESTAMP</creationTime>
                        <!--入口主类-->
                        <mainClass>bici.bzlj.dataprocess.decode.DecodeApplication</mainClass>
                        <!--配置使用的时区-->
                        <environment>
                            <TZ>Asia/Shanghai</TZ>
                        </environment>
                        <labels>
                            <git.commit.user.name>${git.commit.user.name}</git.commit.user.name>
                            <git.commit.time>${git.commit.time}</git.commit.time>
                            <git.branch>${git.branch}</git.branch>
                            <git.commit.id>${git.commit.id}</git.commit.id>
                        </labels>
                        <appRoot>/opt/apps</appRoot>
                        <jvmFlags>
                            <jvmFlag>--enable-preview</jvmFlag>
                        </jvmFlags>
                    </container>
                    <allowInsecureRegistries>true</allowInsecureRegistries>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>21</source>
                    <target>21</target>
                    <compilerArgs>--enable-preview</compilerArgs>
                </configuration>
            </plugin>

        </plugins>
    </build>
    <repositories>
        <repository>
            <id>bici</id>
            <name>bici nexus</name>
            <url>https://nexus.bicisims.com/repository/maven-public/</url>
        </repository>
    </repositories>
    <pluginRepositories>
        <pluginRepository>
            <id>bici</id>
            <name>bici nexus</name>
            <url>https://nexus.bicisims.com/repository/maven-public/</url>
        </pluginRepository>
    </pluginRepositories>

    <profiles>
        <profile>
            <id>upload</id>
            <build>
                <plugins>
                    <plugin>
                        <!--打包时去除第三方依赖-->
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                        <configuration>
                            <layout>ZIP</layout>
                            <includes>
                                <include>
                                    <groupId>non-exists</groupId>
                                    <artifactId>non-exists</artifactId>
                                </include>
                            </includes>
                        </configuration>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-dependency-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>copy-dependencies</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>copy-dependencies</goal>
                                </goals>
                                <configuration>
                                    <!--target/libs是依赖jar包的输出目录，根据自己喜好配置-->
                                    <outputDirectory>target/libs</outputDirectory>
                                    <excludeTransitive>false</excludeTransitive>
                                    <stripVersion>false</stripVersion>
                                    <includeScope>runtime</includeScope>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>