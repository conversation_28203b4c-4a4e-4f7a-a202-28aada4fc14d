{"Table2": {"meta": {"columns": [{"pos": 0, "name": "resid", "descName": "<PERSON><PERSON><PERSON>"}, {"pos": 1, "name": "resname", "descName": "Cresname"}, {"pos": 2, "name": "fname", "descName": "Cfname"}, {"pos": 3, "name": "desc", "descName": "Cdesc"}]}, "rows": [["30000020", "EPESFORM", "", "测试"]]}, "Table1": {"meta": {"columns": [{"pos": 0, "name": "companycode", "descName": "Ccompanycode"}, {"pos": 1, "name": "groupname4j", "descName": "Cgroupname4j"}, {"pos": 2, "name": "groupid", "descName": "Cgroupid"}, {"pos": 3, "name": "groupname", "descName": "Cgroupname"}, {"pos": 4, "name": "appname", "descName": "Cappname"}, {"pos": 5, "name": "restype", "descName": "Crestype"}]}, "rows": [["1517", "EPESFORM_00", "40000279", "测试[EPESFORM_00]", "BWTYS0", "form"]]}, "telegramId": "R_BE_ES_21"}