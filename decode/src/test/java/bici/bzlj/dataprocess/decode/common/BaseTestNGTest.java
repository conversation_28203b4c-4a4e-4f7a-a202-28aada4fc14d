package bici.bzlj.dataprocess.decode.common;

import bici.bzlj.dataprocess.decode.DecodeApplication;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.testng.AbstractTestNGSpringContextTests;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;

@SpringBootTest(classes = DecodeApplication.class,webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class BaseTestNGTest extends AbstractTestNGSpringContextTests {
    
    @BeforeClass(alwaysRun = true)
    public void setupClass() {
        System.out.println("==== 初始化测试环境 ====");
    }
    @AfterClass(alwaysRun = true)
    public void tearDownClass() {
        System.out.println("==== 清理测试环境 ====");
    }
}