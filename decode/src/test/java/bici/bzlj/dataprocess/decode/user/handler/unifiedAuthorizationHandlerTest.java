package bici.bzlj.dataprocess.decode.user.handler;

import bici.bzlj.dataprocess.core.event.MessageEvent;
import bici.bzlj.dataprocess.core.handler.IMessageHandler;
import bici.bzlj.dataprocess.core.producer.MessageEventProducer;
import bici.bzlj.dataprocess.core.utils.JsonUtils;
import bici.bzlj.dataprocess.core.utils.SnowflakeUtils;
import bici.bzlj.dataprocess.decode.common.BaseTestNGTest;
import bici.bzlj.dataprocess.decode.unifiedauthorization.group.entity.SyncAddGroupUsersEntity;
import bici.bzlj.dataprocess.decode.unifiedauthorization.group.entity.SyncAddNewChildGroupEntity;
import bici.bzlj.dataprocess.decode.unifiedauthorization.group.entity.SyncDeleteGroupUsersEntity;
import bici.bzlj.dataprocess.decode.unifiedauthorization.group.entity.SyncGroupEntity;
import bici.bzlj.dataprocess.decode.unifiedauthorization.resource.entity.SyncAddResourceGroupEntity;
import bici.bzlj.dataprocess.decode.unifiedauthorization.user.entity.SyncUserEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;
import org.testng.collections.Maps;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.Map;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2025/5/19 10:10
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
public class unifiedAuthorizationHandlerTest extends BaseTestNGTest {
    //    @Autowired
    private IMessageHandler syncUserHandler;
    @Autowired
    private MessageEventProducer messageEventProducer;
    private MessageEvent messageEvent;
    private Map<String, Object> currentHandleContext;
    private Map<String, Object> handlesContext;
//    private String messageType = "R_BE_ES_14";
//    private String messageType = "R_BE_ES_15";
//    private String messageType = "R_BE_ES_16";
    private String messageType = "R_BE_ES_17";
//    private String messageType = "R_BE_ES_18";
//    private String messageType = "R_BE_ES_21";

    private Map<String, Class> classMap = Map.of(
            "R_BE_ES_14", SyncAddNewChildGroupEntity.class,
            "R_BE_ES_15", SyncUserEntity.class,
            "R_BE_ES_16", SyncGroupEntity.class,
            "R_BE_ES_17", SyncAddGroupUsersEntity.class,
            "R_BE_ES_18", SyncDeleteGroupUsersEntity.class,
            "R_BE_ES_21", SyncAddResourceGroupEntity.class
    );


    @BeforeClass
    public void before() throws IOException {
        currentHandleContext = Maps.newHashMap();
        handlesContext = Maps.newHashMap();
        messageEvent = new MessageEvent<>();
        messageEvent.setMessageId(SnowflakeUtils.nextStrId());
        messageEvent.setMessageType(messageType);

        // 从 resources 目录读取 JSON 文件
        ClassPathResource resource = new ClassPathResource(messageType + ".json");
        String jsonContent = Files.readString(resource.getFile().toPath(), StandardCharsets.UTF_8);
//        messageEvent.setPayload(JsonUtils.fromJson(jsonContent, SyncUserEntity.class));
        messageEvent.setPayload(JsonUtils.fromJson(jsonContent, classMap.get(messageType)));
    }


    @Test
    public void testhandleMessage() throws InterruptedException {
        messageEventProducer.pushMessage(messageEvent);
        Thread.sleep(10000);
    }

    @Test
    public void testHandlePre() {
        syncUserHandler.handlePre(messageEvent, currentHandleContext, handlesContext);
    }

    @Test
    public void testHandle() {
        syncUserHandler.handle(messageEvent, currentHandleContext, handlesContext);
    }

    @Test
    public void testHandlePost() {
        syncUserHandler.handlePost(messageEvent, currentHandleContext, handlesContext);
    }

    @Test
    public void testHandleError() {
        syncUserHandler.handleError(messageEvent, currentHandleContext, handlesContext, new Exception("test"));
    }
}