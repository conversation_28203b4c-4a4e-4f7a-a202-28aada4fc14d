package bici.bzlj.dataprocess.decode.dataConvert.service;

import bici.bzlj.dataprocess.decode.dataConvert.entity.DataMappingConfig;
import bici.bzlj.dataprocess.decode.dataConvert.policy.ExcelImportPolicy;

import java.util.List;
import java.util.Map;

public interface IDataMappingConfigService extends ExcelImportPolicy {

    String importSyncConfig(List<DataMappingConfig> dataMappingConfigs);

    Map<String, DataMappingConfig> getDataMappingConfigMapByServiceId(String serviceId,String  type);

    String processData(Object data, Map<String, DataMappingConfig> configMap);
}

