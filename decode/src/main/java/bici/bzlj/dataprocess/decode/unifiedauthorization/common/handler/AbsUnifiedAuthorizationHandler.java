package bici.bzlj.dataprocess.decode.unifiedauthorization.common.handler;

import bici.bzlj.dataprocess.core.event.MessageEvent;
import bici.bzlj.dataprocess.core.handler.IMessageHandler;
import bici.bzlj.dataprocess.core.utils.JsonUtils;
import bici.bzlj.dataprocess.decode.unifiedauthorization.common.entity.BaseEntity;
import bici.bzlj.dataprocess.decode.unifiedauthorization.common.entity.Block;
import bici.bzlj.dataprocess.decode.unifiedauthorization.common.entity.Column;
import bici.bzlj.dataprocess.decode.unifiedauthorization.common.entity.Meta;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.stream.function.StreamBridge;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2025/6/16 17:09
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */

@Slf4j
public abstract class AbsUnifiedAuthorizationHandler<T extends BaseEntity> implements IMessageHandler<T> {
    public String desc;

    final ZoneOffset offset = ZoneOffset.UTC;

    public final StreamBridge streamBridge;

    protected AbsUnifiedAuthorizationHandler(StreamBridge streamBridge) {
        this.streamBridge = streamBridge;
    }


    /**
     * 消息处理前
     *
     * @param event                消息事件
     * @param currentHandleContext 处理上下文
     */

    @Override
    public void handlePost(MessageEvent<T> event, Map<String, Object> currentHandleContext, Map<String, Object> handlesContext) {
        LocalDateTime startTime = (LocalDateTime) currentHandleContext.get("startTime");
        long startMillis = startTime.atOffset(offset).toInstant().toEpochMilli();
        long endMillis = LocalDateTime.now().atOffset(offset).toInstant().toEpochMilli();
        String telegramId = (String) currentHandleContext.get("telegramId");
        UnifiedAuthorizationMessageInfo<Object> forwardMessage = new UnifiedAuthorizationMessageInfo<>();
        forwardMessage.setTelegramId(telegramId);
        forwardMessage.setSendTime(endMillis);
        forwardMessage.setPayload(currentHandleContext.get(telegramId));
        streamBridge.send((String) currentHandleContext.get("forward_topic"), forwardMessage);

        log.info("{}消息{}处理完成,耗时:{}ms", getImplDesc(event.getMessageType()),
                event.getMessageId(), endMillis - startMillis);
    }

    public String getImplDesc(String messageType) {
        if (StringUtils.isNotBlank(desc)) {
            return desc;
        }
        this.desc = getDesc(messageType);
        return desc;
    }

    /**
     * 错误处理
     *
     * @param event                错误消息事件
     * @param currentHandleContext 处理上下文
     * @param e                    错误信息
     */
    @Override
    public void handleError(MessageEvent<T> event, Map<String, Object> currentHandleContext, Map<String, Object> handlesContext, Exception e) {
        log.error("{}消息{}处理异常:{}", getImplDesc(event.getMessageType()), event.getMessageId(), e.getMessage(), e);
    }

    @Override
    public void handlePre(MessageEvent<T> event, Map<String, Object> currentHandleContext, Map<String, Object> handlesContext) {
        currentHandleContext.put("startTime", LocalDateTime.now());
        log.info("{}消息{}开始处理", getImplDesc(event.getMessageType()), event.getMessageId());

        T payload;
        Object rawPayload = event.getPayload();

        // 检查payload是否为字符串，如果是则需要反序列化
        if (rawPayload instanceof String) {
            String jsonPayload = (String) rawPayload;
            try {
                // 获取泛型类型并反序列化
                Class<T> entityClass = getEntityClass();
                payload = JsonUtils.fromJson(jsonPayload, entityClass);
                // 更新event中的payload为反序列化后的对象
                event.setPayload(payload);
            } catch (Exception e) {
                log.error("反序列化payload失败: {}", e.getMessage(), e);
                throw new RuntimeException("反序列化payload失败", e);
            }
        } else {
            // 如果不是字符串，直接转换
            payload = (T) rawPayload;
        }

        currentHandleContext.put("telegramId", payload.getTelegramId());
    }

    /**
     * 获取泛型实体类型
     *
     * @return 实体类的Class对象
     */
    @SuppressWarnings("unchecked")
    private Class<T> getEntityClass() {
        Type superClass = getClass().getGenericSuperclass();
        if (superClass instanceof ParameterizedType) {
            ParameterizedType parameterizedType = (ParameterizedType) superClass;
            Type[] typeArguments = parameterizedType.getActualTypeArguments();
            if (typeArguments.length > 0) {
                return (Class<T>) typeArguments[0];
            }
        }
        throw new RuntimeException("无法获取泛型类型，请检查类定义");
    }

    /**
     * 处理Block数据，将Meta和rows映射为JsonNode数组
     *
     * @param block 数据块
     * @return JsonNode数组
     */
    protected List<JsonNode> processBlockData(Block block) {
        if (block == null) {
            return Lists.newArrayList();
        }
        Meta meta = block.getMeta();
        List<List<Object>> rows = block.getRows();
        List<Column> columns = meta.getColumns();
        List<JsonNode> result = new ArrayList<>();
        ObjectMapper mapper = new ObjectMapper();
        for (List<Object> row : rows) {
            ObjectNode node = mapper.createObjectNode();
            for (int i = 0; i < columns.size(); i++) {
                Column column = columns.get(i);
                Object o = row.get(i);
                if (ObjectUtils.isEmpty(o)) {
                    node.putNull(column.getName());
                    continue;
                }
                if (ObjectUtils.isNotEmpty(column.getType()) && column.getType().equals("N")) {
                    node.put(column.getName(), Integer.valueOf(o.toString()));
                } else {
                    node.put(column.getName(), o.toString());
                }
            }
            result.add(node);
        }

        return result;
    }

    /**
     * 数据转换
     *
     * @param nodes 节点
     * @return List<Map < String, Object>>
     */
    protected List<Map<String, Object>> dataConvert(List<JsonNode> nodes) {
        List<Map<String, Object>> result = Lists.newArrayList();
        for (JsonNode node : nodes) {
            Map<String, Object> map = JsonUtils.toMap(node);
            result.add(map);
        }
        return result;
    }

    ;

    /**
     * 合并父子关系的数据
     *
     * @param parent    父数据
     * @param childMaps 子数据集合
     * @return List<Map < String, Object>>
     */
    protected List<Map<String, Object>> mergeParentChildData(Map<String, Object> parent, List<Map<String, Object>> childMaps) {
        for (String s : parent.keySet()) {
            for (Map<String, Object> childMap : childMaps) {
                childMap.put("PARENT_" + s, parent.get(s));
            }
        }
        return childMaps;
    }


    /**
     * 处理父-子关系数据
     *
     * @param groupInfo 父数据
     * @param userInfo  子数据
     * @return List<Map < String, Object>>
     */
    protected List<Map<String, Object>> getDealParentChildData(Block groupInfo, Block userInfo) {
        List<Map<String, Object>> parentMaps = dataConvert(processBlockData(groupInfo));
        List<Map<String, Object>> childMaps = dataConvert(processBlockData(userInfo));
        List<Map<String, Object>> updateData = mergeParentChildData(parentMaps.getFirst(), childMaps);
        return updateData;
    }
}
