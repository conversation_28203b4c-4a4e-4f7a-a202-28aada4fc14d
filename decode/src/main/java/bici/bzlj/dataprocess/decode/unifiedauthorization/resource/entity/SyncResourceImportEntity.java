package bici.bzlj.dataprocess.decode.unifiedauthorization.resource.entity;

import bici.bzlj.dataprocess.decode.unifiedauthorization.common.entity.BaseEntity;
import bici.bzlj.dataprocess.decode.unifiedauthorization.common.entity.Block;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 资源导入同步消息
 *
 * <AUTHOR>
 * @date 2025/6/18 15:53
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SyncResourceImportEntity extends BaseEntity {
    @JsonProperty("TESFORMRESINFO")
    private Block tesFormResInfo;
    @JsonProperty("TESBUTTONRESINFO")
    private Block tesButtonResInfo;
    @JsonProperty("TESFORMRESINFO_RES")
    private Block tesFormResInfoRes;
    @JsonProperty("TESBUTTONRESINFO_RES")
    private Block tesButtonResInfoRes;
}
