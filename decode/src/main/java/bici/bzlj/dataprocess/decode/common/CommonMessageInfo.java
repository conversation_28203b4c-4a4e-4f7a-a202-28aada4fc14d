package bici.bzlj.dataprocess.decode.common;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * kafka 电文消息内容实体
 *
 * <AUTHOR>
 * @date 2025/5/16 15:57
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CommonMessageInfo<T> {
    /**
     * 服务ID/电文号
     */
    private String id;
    /**
     * 电文分类
     */
    private String type;
    /**
     * 电文接收时间
     */
    private Long receiptTime;
    /**
     * 电文发送时间
     */
    private Long sendTime;
    /**
     * 电文内容
     */
    private T payload;

    /**
     * 转化后的数据
     */
    private String convertData;
}
