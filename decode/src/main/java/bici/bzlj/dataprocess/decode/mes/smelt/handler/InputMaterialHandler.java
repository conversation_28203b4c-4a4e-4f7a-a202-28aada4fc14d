package bici.bzlj.dataprocess.decode.mes.smelt.handler;

import bici.bzlj.dataprocess.core.annotation.MessageHandler;
import bici.bzlj.dataprocess.core.event.MessageEvent;
import bici.bzlj.dataprocess.core.utils.JsonUtils;
import bici.bzlj.dataprocess.decode.util.ImplantAttrUtil;
import bici.bzlj.dataprocess.decode.common.ForwardHandler;
import bici.bzlj.dataprocess.decode.mes.smelt.entity.InputMaterialEntity;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.stream.function.StreamBridge;

import java.util.Map;
import java.util.Objects;


/**
 * TODO
 *  原料消耗实绩电文
 * <AUTHOR>
 * @date 2025/5/15 16:17
 */
@MessageHandler(messageType = {"DZMTP2"}, desc = "投入物料实绩电文(DYMTN2)")
@Slf4j
public class InputMaterialHandler extends ForwardHandler<String> {

    private static final String SERVICE_ID = "input_material";

    @Override
    public String getGyckServiceId() {
        return SERVICE_ID;
    }

    protected InputMaterialHandler(StreamBridge streamBridge) {
        super(streamBridge);
    }

    /**
     * 消息处理
     *
     * @param event         消息事件
     * @param currentHandleContext 处理上下文
     */
    @Override
    public void handle(MessageEvent<String> event, Map<String, Object> currentHandleContext, Map<String, Object> handlesContext) {
        ArrayNode inputMaterialNodes = (ArrayNode)JsonUtils.toJsonNode(Objects.requireNonNull(event.getPayload())).get("LOOP");
        ObjectMapper mapper = new ObjectMapper();
        ArrayNode nodes = mapper.createArrayNode();
        inputMaterialNodes.iterator().forEachRemaining(inputMaterialNode -> {
            InputMaterialEntity inputMaterial = JsonUtils.fromJson(inputMaterialNode.toString(), InputMaterialEntity.class);
            ObjectNode node = mapper.createObjectNode();
            Map<String, Object> physicalAttr = ImplantAttrUtil.implantConvertMap(inputMaterial, "physicalAttr");
            Map<String, Object> specificationAttr = ImplantAttrUtil.implantConvertMap(inputMaterial, "specificationAttr");

            // 任务编号 必须
            node.put("taskCode", inputMaterial.getPlanNo());
            // 熔炼号
            node.put("heatNumber", inputMaterial.getHeatNo());
            // 物料编号 必须
            node.put("materialCode", inputMaterial.getInMatNo());
            // 物料名称 ==  物料编号
            node.put("materialName", inputMaterial.getInMatNo());
            // 物理属性
            node.put("physicalAttr", JsonUtils.toJson(physicalAttr));
            // 规格属性
            node.put("specificationAttr", JsonUtils.toJson(specificationAttr));
            nodes.add(node);
        });
        currentHandleContext.put("serviceId", SERVICE_ID);
        currentHandleContext.put(SERVICE_ID, JsonUtils.toJson(nodes));
    }

}
