package bici.bzlj.dataprocess.decode.unifiedauthorization.group.handler;

import bici.bzlj.dataprocess.core.annotation.MessageHandler;
import bici.bzlj.dataprocess.core.event.MessageEvent;
import bici.bzlj.dataprocess.core.utils.JsonUtils;
import bici.bzlj.dataprocess.decode.unifiedauthorization.common.entity.Block;
import bici.bzlj.dataprocess.decode.unifiedauthorization.common.handler.AbsUnifiedAuthorizationHandler;
import bici.bzlj.dataprocess.decode.unifiedauthorization.group.entity.SyncAddNewChildGroupEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.stream.function.StreamBridge;

import java.util.List;
import java.util.Map;

/**
 * 新增子群组同步消息
 *
 * <AUTHOR>
 * @date 2025/6/17 10:28
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@MessageHandler(messageType = "R_BE_ES_14", desc = "新增子群组同步消息(R_BE_ES_14)")
@Slf4j
public class SyncAddNewChildGroupHandler extends AbsUnifiedAuthorizationHandler<SyncAddNewChildGroupEntity> {
    protected SyncAddNewChildGroupHandler(StreamBridge streamBridge) {
        super(streamBridge);
    }


    /**
     * 消息处理
     *
     * @param event                消息事件
     * @param currentHandleContext 当前handle上下文
     * @param handlesContext       当前handle组上下文
     */
    @Override
    public void handle(MessageEvent<SyncAddNewChildGroupEntity> event,
                       Map<String, Object> currentHandleContext,
                       Map<String, Object> handlesContext) {
        SyncAddNewChildGroupEntity payload = event.getPayload();
        Block parent = payload.getParent();
        Block child = payload.getChild();
        // 处理新增用户数据
        List<Map<String, Object>> parentMaps = dataConvert(processBlockData(parent));
        // 处理更新用户数据
        List<Map<String, Object>> childMaps = dataConvert(processBlockData(child));
        List<Map<String, Object>> updateData = mergeParentChildData(parentMaps.getFirst(), childMaps);
        log.info("updateData[0]:{}", JsonUtils.toJson(updateData.getFirst()));
        Map<String, Object> result = Map.of("updateData", updateData);
        currentHandleContext.put("forward_topic", "unified_authorization");
        currentHandleContext.put(payload.getTelegramId(), result);
    }


}
