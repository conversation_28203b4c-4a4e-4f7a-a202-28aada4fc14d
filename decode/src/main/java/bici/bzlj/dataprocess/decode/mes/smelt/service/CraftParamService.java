package bici.bzlj.dataprocess.decode.mes.smelt.service;

import bici.bzlj.dataprocess.decode.mes.smelt.entity.CraftParamAckEntity;
import bici.bzlj.dataprocess.decode.mes.smelt.entity.CraftParamEntity;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;

public class CraftParamService {

    public static JsonNode analysisParams(CraftParamEntity payload){
        //转化为工艺参数
        List<CraftParamEntity.Loop> loops = payload.getLoops();
        String matNo = payload.getMatNo();
        String orderNo = payload.getOrderNo();
        String planNo = payload.getPlanNo();
        if (StringUtils.isBlank(matNo) || StringUtils.isBlank(orderNo) || StringUtils.isBlank(planNo)) {
            throw new RuntimeException(String.format("MTG1TB:缺少参数；入口材料号：%s,合同号：%s,计划号：%s", matNo, orderNo, planNo));
        }

        return dealParams(matNo, orderNo, planNo, loops);

    }

    private static JsonNode dealParams(String matNo,String orderNo,String planNo, List<CraftParamEntity.Loop> loops) {
        if (CollectionUtils.isEmpty(loops)) {
            throw new RuntimeException("MTG1TB: 工序参数列表为空");
        }
        ObjectMapper mapper = new ObjectMapper();
        ObjectNode node = mapper.createObjectNode();
        ArrayNode craftParams = mapper.createArrayNode();
        node.put("matNo", matNo);
        node.put("orderNo", orderNo);
        node.put("planNo", planNo);
        loops.forEach(loop -> {
            ObjectNode craftParam = mapper.createObjectNode();
            craftParam.put("paramName", loop.getCpItemName());
            craftParam.put("targetValue", loop.getCpItemAim());
            craftParam.put("minValue", loop.getCpItemMin());
            craftParam.put("maxValue", loop.getCpItemMax());
            craftParam.put("taskCode", planNo);
            ObjectNode extendAttr = mapper.createObjectNode();
            extendAttr.put("cpItemLevel", loop.getCpItemLevel());
            extendAttr.put("responsePlanDesc", loop.getResponsePlanDesc());
            extendAttr.put("prcCharactRec", loop.getPrcCharactRec());
            extendAttr.put("prcCharactPro", loop.getPrcCharactPro());
            extendAttr.put("prcCharactAux", loop.getPrcCharactAux());
            extendAttr.put("prcCharactIng", loop.getPrcCharactIng());
            extendAttr.put("prcCharactCha", loop.getPrcCharactCha());
            extendAttr.put("prcCharactSme", loop.getPrcCharactSme());
            extendAttr.put("prcCharactCast", loop.getPrcCharactCast());
            craftParam.put("extendAttr", extendAttr);
            craftParams.add(craftParam);
        });
        node.put("craftParams", craftParams);
        return node;
    }

    public static JsonNode analysisParamAck(CraftParamAckEntity payload){
        String matNo = payload.getMatNo();
        String orderNo = payload.getKey1();
        String flag = payload.getReplyFlag();
        String desc = payload.getReplyDesc();
        if (StringUtils.isBlank(matNo) || StringUtils.isBlank(orderNo) || StringUtils.isBlank(flag)) {
            throw new RuntimeException(String.format("G1MTTB:缺少参数；入口材料号：%s,合同号：%s,应答标记：%s", matNo, orderNo, flag));
        }
        if (!StringUtils.equals(flag, "0") && !StringUtils.equals(flag, "1")) {
            throw new RuntimeException(String.format("G1MTTB:参数错误；应答标识：%s", flag));
        }
        ObjectMapper mapper = new ObjectMapper();
        ObjectNode node = mapper.createObjectNode();
        node.put("matNo", matNo);
        node.put("orderNo", orderNo);
        node.put("replyFlag", flag);
        node.put("replyDesc", desc);
        return node;
    }

}
