package bici.bzlj.dataprocess.decode.mes.smelt.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * G1MTTB、G1MTTC
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CraftParamAckEntity {

    /**
     * 入口材料号
     */
    @JsonProperty("MAT_NO")
    private String matNo;

    /**
     * 合同号
     */
    @JsonProperty("KEY_1")
    private String key1;

    /**
     * 应答标记
     * 0:接收正常1:异常
     */
    @JsonProperty("REPLY_FLAG")
    private String replyFlag;

    /**
     * 应答说明
     * 异常原因
     */
    @JsonProperty("REPLY_DESC")
    private String replyDesc;
}
