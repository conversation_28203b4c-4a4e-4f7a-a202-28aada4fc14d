package bici.bzlj.dataprocess.decode.mes.smelt.convert;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Set;

@Data
public class Task {

    /**
     * 任务编号 必须
     */
    private String taskCode;

    /**
     * 计划开始时间
     */
    private String planStartTime;

    /**
     * 工序编号 必须
     */
    private String processCode;


    /**
     * 计划结束时间
     */
    private String planEndTime;

    /**
     * 计划生产重量
     */
    private BigDecimal planWeight;

    /**
     * 任务名称 == 任务编号
     */
    private String taskName;

    /**
     * 计划生产数量
     */
    private Integer planQuantity;

    /**
     * 设备 必须
     */
    private String equipmentCode;

    /**
     * 炉号
     */
    private String heatNo;

    /**
     * 分厂号
     */
    private String plantCode;

    /**
     * 扩展属性，不确定的字段，转化为驼峰往里放
     */
    private Map<String, Object> extendAttr;
}
