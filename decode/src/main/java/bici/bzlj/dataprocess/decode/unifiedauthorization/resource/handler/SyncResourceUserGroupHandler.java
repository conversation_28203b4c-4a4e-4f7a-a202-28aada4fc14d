package bici.bzlj.dataprocess.decode.unifiedauthorization.resource.handler;

import bici.bzlj.dataprocess.core.annotation.MessageHandler;
import bici.bzlj.dataprocess.core.event.MessageEvent;
import bici.bzlj.dataprocess.decode.unifiedauthorization.common.entity.Block;
import bici.bzlj.dataprocess.decode.unifiedauthorization.common.handler.AbsUnifiedAuthorizationHandler;
import bici.bzlj.dataprocess.decode.unifiedauthorization.resource.entity.SyncResourceUserGroupEntity;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.stream.function.StreamBridge;

import java.util.List;
import java.util.Map;

/**
 * 资源组人员组关系维护同步消息
 *
 * <AUTHOR>
 * @date 2025/6/18 16:22
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@MessageHandler(messageType = "R_BE_ES_23", desc = "资源组人员组关系维护同步消息(R_BE_ES_23)")
@Slf4j
public class SyncResourceUserGroupHandler extends AbsUnifiedAuthorizationHandler<SyncResourceUserGroupEntity> {
    protected SyncResourceUserGroupHandler(StreamBridge streamBridge) {
        super(streamBridge);
    }



    /**
     * 消息处理
     *
     * @param event                消息事件
     * @param currentHandleContext 当前handle上下文
     * @param handlesContext       当前handle组上下文
     */
    @Override
    public void handle(MessageEvent<SyncResourceUserGroupEntity> event,
                       Map<String, Object> currentHandleContext,
                       Map<String, Object> handlesContext) {
        SyncResourceUserGroupEntity payload = event.getPayload();
        Block groupResAdd = payload.getGroupResAdd();
        Block groupResRmv = payload.getGroupResRmv();
        Block resGroupAdd = payload.getResGroupAdd();
        Block resGroupRmv = payload.getResGroupRmv();


        List<JsonNode> groupResAddInfos = processBlockData(groupResAdd);
        List<JsonNode> groupResRmvInfos = processBlockData(groupResRmv);
        List<JsonNode> resGroupAddInfos = processBlockData(resGroupAdd);
        List<JsonNode> resGroupRmvInfos = processBlockData(resGroupRmv);

        Map<String, Object> result = Map.of("groupResAddInfos", dataConvert(groupResAddInfos),
                "groupResRmvInfos", dataConvert(groupResRmvInfos),
                "resGroupAddInfos", dataConvert(resGroupAddInfos),
                "resGroupRmvInfos", dataConvert(resGroupRmvInfos));
        currentHandleContext.put("forward_topic", "unified_authorization");
        currentHandleContext.put(payload.getTelegramId(), result);

    }
}
