package bici.bzlj.dataprocess.decode.unifiedauthorization.common.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserInfo {
    @JsonProperty("loginName")
    private String loginName;

    @JsonProperty("userId")
    private String userId;

    @JsonProperty("username")
    private String username;
}
