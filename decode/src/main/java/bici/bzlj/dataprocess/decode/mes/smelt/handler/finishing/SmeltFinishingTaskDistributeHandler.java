package bici.bzlj.dataprocess.decode.mes.smelt.handler.finishing;

import bici.bzlj.dataprocess.core.annotation.MessageHandler;
import bici.bzlj.dataprocess.core.event.MessageEvent;
import bici.bzlj.dataprocess.core.utils.JsonUtils;
import bici.bzlj.dataprocess.decode.common.ForwardHandler;
import bici.bzlj.dataprocess.decode.dataConvert.cache.ProcessCodeConfigCache;
import bici.bzlj.dataprocess.decode.dataConvert.entity.ProcessCodeConfig;
import bici.bzlj.dataprocess.decode.mes.smelt.handler.finishing.common.DealJsonNode;
import bici.bzlj.dataprocess.decode.util.StringUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.stream.function.StreamBridge;

import java.util.Map;
import java.util.Objects;


/**
 * TODO
 *  特冶精整任务下发电文监听
 *
 * <AUTHOR>
 * @date 2025/5/15 16:17
 */
@MessageHandler(messageType = {"MTDXF1", "MTDYF1", "MTDZF1"}, desc = "任务下发电文监听")
@Slf4j
public class SmeltFinishingTaskDistributeHandler extends ForwardHandler<String> {

    protected SmeltFinishingTaskDistributeHandler(StreamBridge streamBridge) {
        super(streamBridge);
    }

    @Override
    public String getGyckServiceId() {
        return SERVICE_ID;
    }

    private static final String SERVICE_ID = "task_Distribute";

    /**
     * 消息处理
     *
     * @param event                消息事件
     * @param currentHandleContext 处理上下文
     */
    @Override
    public void handle(MessageEvent<String> event, Map<String, Object> currentHandleContext, Map<String, Object> handlesContext) {
        Map<String, ProcessCodeConfig> configMap = ProcessCodeConfigCache.get(event.getMessageType());
        String convertData = event.getConvertData();
        JsonNode jsonNode = JsonUtils.toJsonNode(convertData);
        if (jsonNode instanceof ArrayNode) {
            jsonNode.forEach(node -> {
                DealJsonNode.modifyTaskCode(node);
                setProcessCode(node, configMap);
            });
        }else{
            DealJsonNode.modifyTaskCode(jsonNode);
            setProcessCode(jsonNode, configMap);
        }
        event.setConvertData(JsonUtils.toJson(jsonNode));
        // 将处理结果放入上下文
        currentHandleContext.put("serviceId", SERVICE_ID);
        currentHandleContext.put(SERVICE_ID, event.getConvertData());
    }

    /**
     * 设置工序代码
     * @param jsonNode
     * @param configMap
     */
    private void setProcessCode(JsonNode jsonNode, Map<String, ProcessCodeConfig> configMap) {
        JsonNode processCodeNode = jsonNode.get("processCode");
        ProcessCodeConfig config = null;
        if (Objects.nonNull(processCodeNode) && StringUtils.isNotEmpty(processCodeNode.asText())) {
            config = configMap.get(processCodeNode.asText());
        }
        if (Objects.isNull(config)) {
            return;
        }
        // 将processCode添加到当前节点
        ((ObjectNode) jsonNode).put("processCode", config.getProcessCode());
    }


}
