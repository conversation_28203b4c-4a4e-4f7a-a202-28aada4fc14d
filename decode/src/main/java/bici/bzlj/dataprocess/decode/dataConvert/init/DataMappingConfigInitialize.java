package bici.bzlj.dataprocess.decode.dataConvert.init;

import bici.bzlj.dataprocess.decode.dataConvert.cache.DataMappingConfigCache;
import bici.bzlj.dataprocess.decode.dataConvert.cache.ProcessCodeConfigCache;
import bici.bzlj.dataprocess.decode.dataConvert.service.IDataMappingConfigService;
import bici.bzlj.dataprocess.decode.dataConvert.service.IProcessCodeConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2025/7/2 16:46
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@Configuration
@RequiredArgsConstructor
public class DataMappingConfigInitialize {
    private final IDataMappingConfigService dataMappingConfigService;

    private final IProcessCodeConfigService processCodeConfigService;

    /**
     * 初始化缓存
     *
     * @return 缓存对象
     */
    @Bean
    public DataMappingConfigCache getDataMappingConfigCache() {
        return new DataMappingConfigCache(dataMappingConfigService);
    }

    @Bean
    public ProcessCodeConfigCache getProcessCodeConfigCache() {
        return new ProcessCodeConfigCache(processCodeConfigService);
    }
}
