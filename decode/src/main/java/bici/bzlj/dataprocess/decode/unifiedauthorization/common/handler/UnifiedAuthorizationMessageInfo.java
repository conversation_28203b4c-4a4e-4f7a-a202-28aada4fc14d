package bici.bzlj.dataprocess.decode.unifiedauthorization.common.handler;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * kafka 解析完成数据转发实体
 *
 * <AUTHOR>
 * @date 2025/6/04 15:57
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UnifiedAuthorizationMessageInfo<T> {
    /**
     * 服务id
     */
    private String telegramId;
    /**
     * 发送时间
     */
    private Long sendTime;
    /**
     * 内容
     */
    private T payload;
}
