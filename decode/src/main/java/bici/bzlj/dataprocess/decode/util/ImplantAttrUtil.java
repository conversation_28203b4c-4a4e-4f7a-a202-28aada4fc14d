package bici.bzlj.dataprocess.decode.util;

import bici.bzlj.dataprocess.decode.annotation.ImplantAttr;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

public class ImplantAttrUtil {

    public static Map<String, Object> implantConvertMap(Object obj, String implantName) {
        if (StringUtils.isBlank(implantName)) throw new RuntimeException("扩展属性名称不能为空");
        Map<String, Object> map = new HashMap<>();
        Field[] fields = obj.getClass().getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            ImplantAttr annotation = field.getAnnotation(ImplantAttr.class);
            if (annotation != null && implantName.equals(annotation.value())) {
                try {
                    String alias = field.getName();
                    if (StringUtils.isNotBlank(annotation.alias())) {
                        alias = annotation.alias();
                    }
                    map.put(alias, field.get(obj));
                } catch (Exception e) {
                    throw new RuntimeException("扩展属性获取失败", e);
                }
            }
        }
        return map;
    }
}
