package bici.bzlj.dataprocess.decode.mes.smelt.handler;

import bici.bzlj.dataprocess.core.annotation.MessageHandler;
import bici.bzlj.dataprocess.core.event.MessageEvent;
import bici.bzlj.dataprocess.core.utils.JsonUtils;
import bici.bzlj.dataprocess.decode.util.ImplantAttrUtil;
import bici.bzlj.dataprocess.decode.common.ForwardHandler;
import bici.bzlj.dataprocess.decode.mes.smelt.convert.Task;
import bici.bzlj.dataprocess.decode.mes.smelt.entity.TaskEntity;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.stream.function.StreamBridge;

import java.util.Map;


/**
 * TODO
 *  任务下发电文监听
 *
 * <AUTHOR>
 * @date 2025/5/15 16:17
 */
@MessageHandler(messageType = {"MTDXF1","MTDZE1","MTDZF1","MTDYF1"}, desc = "任务下发电文监听")
@Slf4j
public class TaskDistributeHandler extends ForwardHandler<String> {

    private static final ObjectMapper objectMapper = new ObjectMapper();
    @Override
    public String getGyckServiceId() {
        return SERVICE_ID;
    }


    protected TaskDistributeHandler(StreamBridge streamBridge) {
        super(streamBridge);
    }

    private static final String SERVICE_ID = "task_Distribute";

    /**
     * 消息处理
     *
     * @param event                消息事件
     * @param currentHandleContext 处理上下文
     */
    @Override
    public void handle(MessageEvent<String> event, Map<String, Object> currentHandleContext, Map<String, Object> handlesContext) {
        TaskEntity payload =
                JsonUtils.fromJson(event.getPayload(), TaskEntity.class)
                 ;
        String task = dealTaskEntity(payload);
        // 将处理结果放入上下文
        currentHandleContext.put("serviceId", SERVICE_ID);
        currentHandleContext.put(SERVICE_ID, task);
    }


    private String dealTaskEntity(TaskEntity payload) {
        Task task = new Task();
        //任务编号 必须
        task.setTaskCode(payload.getPono());
        //设备编号 必须
        task.setEquipmentCode(payload.getMachineNo());
        //计划数量
        task.setPlanQuantity(payload.getPlanNum());
        //计划重量
        task.setPlanWeight(payload.getPlanWt());
        //计划开始时间
        task.setPlanStartTime(payload.getReserStartTime());
        //计划结束时间
        task.setPlanEndTime(payload.getReserEndTime());
        //任务名称 == 任务编号
        task.setTaskName(payload.getPono());
        task.setPlantCode("P01");
        //炉号
        task.setHeatNo(payload.getHeatNo());
        String processCode;
        if (StringUtils.isNotBlank(payload.getWholeBacklogCode())) {
            processCode = payload.getWholeBacklogCode();
        } else {
            throw new RuntimeException("找不到工序");
        }
        //工序编号 必须
        task.setProcessCode(processCode);
        //扩展属性
        task.setExtendAttr(ImplantAttrUtil.implantConvertMap(payload, "extendAttr"));
        try {
            return objectMapper.writeValueAsString(task);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("JSON转换失败", e);
        }
    }

}
