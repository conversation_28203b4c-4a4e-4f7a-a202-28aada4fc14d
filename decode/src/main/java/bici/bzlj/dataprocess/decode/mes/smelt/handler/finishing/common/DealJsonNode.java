package bici.bzlj.dataprocess.decode.mes.smelt.handler.finishing.common;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-08-06 17:05
 */
public class DealJsonNode {

    /**
     * 修改任务编号
     * @param jsonNode
     */
    public static void modifyTaskCode(JsonNode jsonNode){
        JsonNode processCodeNode = jsonNode.get("processCode");
        JsonNode taskCodeNode = jsonNode.get("taskCode");
        if (Objects.nonNull(processCodeNode) && Objects.nonNull(taskCodeNode)) {
            ((ObjectNode) jsonNode).put("taskCode", String.format("%s_%s", taskCodeNode.asText(), processCodeNode.asText()));
            ((ObjectNode) jsonNode).put("taskName", String.format("%s_%s", taskCodeNode.asText(), processCodeNode.asText()));
        }
    }
}
