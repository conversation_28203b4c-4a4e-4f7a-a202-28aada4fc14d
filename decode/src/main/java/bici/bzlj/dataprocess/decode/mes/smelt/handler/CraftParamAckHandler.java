package bici.bzlj.dataprocess.decode.mes.smelt.handler;

import bici.bzlj.dataprocess.core.annotation.MessageHandler;
import bici.bzlj.dataprocess.core.event.MessageEvent;
import bici.bzlj.dataprocess.core.utils.JsonUtils;
import bici.bzlj.dataprocess.decode.common.ForwardHandler;
import bici.bzlj.dataprocess.decode.mes.smelt.entity.CraftParamAckEntity;
import bici.bzlj.dataprocess.decode.mes.smelt.service.CraftParamService;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.stream.function.StreamBridge;

import java.util.Map;
import java.util.Objects;


/**
 * TODO
 *  (应答) 感应_真空/中频感应炉-CP参数电文(G1MTTB，G1MTTC)
 *
 * <AUTHOR>
 * @date 2025/5/15 16:17
 */
@MessageHandler(messageType = {"G1MTTB","G1MTTC"}, desc = {"(应答) 感应_真空感应炉-CP参数电文(G1MTTB)","(应答) 感应_中频感应炉-CP参数电文(G1MTTC)"})
@Slf4j
public class CraftParamAckHandler extends ForwardHandler<CraftParamAckEntity> {

    private static final String SERVICE_ID = "craft_params_ack";

    @Override
    public String getGyckServiceId() {
        return SERVICE_ID;
    }

    protected CraftParamAckHandler(StreamBridge streamBridge) {
        super(streamBridge);
    }
    /**
     * 消息处理
     *
     * @param event         消息事件
     * @param currentHandleContext 处理上下文
     */
    @Override
    public void handle(MessageEvent<CraftParamAckEntity> event, Map<String, Object> currentHandleContext, Map<String, Object> handlesContext) {
        CraftParamAckEntity payload =
                JsonUtils.fromJson(Objects.requireNonNull(JsonUtils.toJson(event.getPayload())), CraftParamAckEntity.class);

        JsonNode jsonNode = CraftParamService.analysisParamAck(payload);
        currentHandleContext.put("serviceId", SERVICE_ID);
        currentHandleContext.put(SERVICE_ID, jsonNode);
    }

}
