package bici.bzlj.dataprocess.decode.mes.forge.handler;

import bici.bzlj.dataprocess.core.annotation.MessageHandler;
import bici.bzlj.dataprocess.core.event.MessageEvent;
import bici.bzlj.dataprocess.core.utils.JsonUtils;
import bici.bzlj.dataprocess.decode.common.ForwardHandler;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.stream.function.StreamBridge;

import java.util.Map;
import java.util.Objects;


/**
 * TODO
 *  任务下发电文监听
 *
 * <AUTHOR>
 * @date 2025/5/15 16:17
 */
@MessageHandler(messageType = {"MTS201", "MTS211", "MTS221", "MTS231", "MTS242"}, desc = "任务下发电文监听(MTS201)")
@Slf4j
public class ForgeTaskDistributeHandler extends ForwardHandler<String> {

    protected ForgeTaskDistributeHandler(StreamBridge streamBridge) {
        super(streamBridge);
    }

    @Override
    public String getGyckServiceId() {
        return SERVICE_ID;
    }

    private static final String SERVICE_ID = "task_Distribute";

    /**
     * 消息处理
     *
     * @param event                消息事件
     * @param currentHandleContext 处理上下文
     */
    @Override
    public void handle(MessageEvent<String> event, Map<String, Object> currentHandleContext, Map<String, Object> handlesContext) {
        switch (event.getMessageType()) {
            case "MTS201":
            case "MTS211":
                String convertData = event.getConvertData();
                JsonNode jsonNode = JsonUtils.toJsonNode(convertData);
                if (jsonNode instanceof ArrayNode) {
                    jsonNode.forEach(node -> {
                        JsonNode equipmentNode = node.get("equipmentCode");
                        if (Objects.nonNull(equipmentNode)) {
                            String processCode = "";
                            try {
                                processCode = equipmentNode.asText().substring(0, 2);
                            } catch (Exception e) {
                                log.error("设备编码长度不足2位: {}", equipmentNode.asText());
                            }
                            // 将processCode添加到当前节点
                            ((ObjectNode) node).put("processCode", processCode);
                        }
                    });
                }
                event.setConvertData(JsonUtils.toJson(jsonNode));
                break;
            default:
                break;
        }

        // 将处理结果放入上下文
        currentHandleContext.put("serviceId", SERVICE_ID);
        currentHandleContext.put(SERVICE_ID, event.getConvertData());
    }


}
