package bici.bzlj.dataprocess.decode.unifiedauthorization.common.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class Sys {
    @JsonProperty("msg")
    private String msg;
    @JsonProperty("traceId")
    private String traceId;
    @JsonProperty("detailMsg")
    private String detailMsg;
    @JsonProperty("name")
    private String name;
    @JsonProperty("msgKey")
    private String msgKey;
    @JsonProperty("descName")
    private String descName;
    @JsonProperty("status")
    private Integer status;
}