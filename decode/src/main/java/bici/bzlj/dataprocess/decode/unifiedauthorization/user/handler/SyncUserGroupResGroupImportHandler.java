package bici.bzlj.dataprocess.decode.unifiedauthorization.user.handler;

import bici.bzlj.dataprocess.core.annotation.MessageHandler;
import bici.bzlj.dataprocess.core.event.MessageEvent;
import bici.bzlj.dataprocess.decode.unifiedauthorization.common.entity.Block;
import bici.bzlj.dataprocess.decode.unifiedauthorization.common.handler.AbsUnifiedAuthorizationHandler;
import bici.bzlj.dataprocess.decode.unifiedauthorization.user.entity.SyncUserGroupResGroupImportEntity;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.stream.function.StreamBridge;

import java.util.List;
import java.util.Map;

/**
 * 用户组资源组关系导入同步消息
 *
 * <AUTHOR>
 * @date 2025/5/15 16:17
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@MessageHandler(messageType = "R_BE_ES_27", desc = "用户组资源组关系导入同步消息(R_BE_ES_27)")
@Slf4j
public class SyncUserGroupResGroupImportHandler extends AbsUnifiedAuthorizationHandler<SyncUserGroupResGroupImportEntity> {
    protected SyncUserGroupResGroupImportHandler(StreamBridge streamBridge) {
        super(streamBridge);
    }

    @Override
    protected List<Map<String, Object>> dataConvert(List<JsonNode> nodes) {
        return List.of();
    }

    /**
     * 消息处理
     *
     * @param event                消息事件
     * @param currentHandleContext 处理上下文
     */
    @Override
    public void handle(MessageEvent<SyncUserGroupResGroupImportEntity> event,
                       Map<String, Object> currentHandleContext,
                       Map<String, Object> handlesContext) {
        SyncUserGroupResGroupImportEntity payload = event.getPayload();


        Block table1 = payload.getTable1();
        Block table2 = payload.getTable2();
        Block compCode = payload.getCompCode();
        List<JsonNode> table1Nodes = processBlockData(table1);
        List<JsonNode> table2Nodes = processBlockData(table2);
        List<JsonNode> compCodeNodes = processBlockData(compCode);
        Map<String, Object> result = Map.of("table1", dataConvert(table1Nodes),
                "table2", dataConvert(table2Nodes),
                "compCode", dataConvert(compCodeNodes));
        currentHandleContext.put("forward_topic", "unified_authorization");
        currentHandleContext.put(payload.getTelegramId(), result);
    }

}
