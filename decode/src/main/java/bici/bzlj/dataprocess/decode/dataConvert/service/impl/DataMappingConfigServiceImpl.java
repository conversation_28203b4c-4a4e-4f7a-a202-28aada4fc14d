package bici.bzlj.dataprocess.decode.dataConvert.service.impl;

import bici.bzlj.dataprocess.core.utils.JsonUtils;
import bici.bzlj.dataprocess.decode.dataConvert.cache.DataMappingConfigCache;
import bici.bzlj.dataprocess.decode.dataConvert.constant.Constants;
import bici.bzlj.dataprocess.decode.dataConvert.entity.DataMappingConfig;
import bici.bzlj.dataprocess.decode.dataConvert.repository.DataMappingConfigRepository;
import bici.bzlj.dataprocess.decode.dataConvert.service.IDataMappingConfigService;
import bici.bzlj.dataprocess.decode.util.StringUtils;
import bici.bzlj.dataprocess.decode.util.poi.ExcelUtil;
import com.beust.jcommander.internal.Lists;
import com.beust.jcommander.internal.Maps;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.InputStream;
import java.util.*;

@Service(value = Constants.EXCEL_IMPORT_POLICY + "dataMapping")
@Slf4j
@RequiredArgsConstructor
public class DataMappingConfigServiceImpl implements IDataMappingConfigService {

    private final DataMappingConfigRepository dataMappingConfigRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importSyncConfig(List<DataMappingConfig> dataMappingConfigs) {
        // 校验导入的配置
        checkConfig(dataMappingConfigs);

        // 获取所有受影响的serviceId，用于后续缓存刷新
        List<String> affectedServiceIds = dataMappingConfigs.stream()
                .map(config -> String.join("__", config.getServiceId(), config.getType()))
                .filter(StringUtils::isNotBlank)
                .distinct()
                .toList();

        // 获取id集合
        List<String> ids = dataMappingConfigs.stream().map(DataMappingConfig::getId).toList();
        // 删除原有配置
        dataMappingConfigRepository.deleteAllById(ids);
        // 插入新配置
        for (DataMappingConfig dataMappingConfig : dataMappingConfigs) {
            dataMappingConfigRepository.insert(dataMappingConfig);
        }

        // 刷新受影响的缓存
        int refreshedCount = refreshCacheForServiceIds(affectedServiceIds);

        log.info("成功导入{}条同步配置，涉及{}个服务，实际刷新缓存{}个",
                dataMappingConfigs.size(), affectedServiceIds.size(), refreshedCount);

        return String.format("成功导入%d条配置，刷新了%d个服务的缓存",
                dataMappingConfigs.size(), refreshedCount);
    }


    /**
     * 校验导入的配置
     *
     * @param dataMappingConfigs 同步配置列表
     */
    private static void checkConfig(List<DataMappingConfig> dataMappingConfigs) {
        if (CollectionUtils.isEmpty(dataMappingConfigs)) {
            throw new RuntimeException("导入配置为空！");
        }
        long countServiceIds = dataMappingConfigs
                .stream()
                .map(DataMappingConfig::getServiceId)
                .filter(StringUtils::isNotBlank)
                .count();
        // 判断serviceId是否存在未填写的
        if (countServiceIds != dataMappingConfigs.size()) {
            throw new RuntimeException("导入配置存在未填写的serviceId！");
        }
        // 判断是否存在未填写的sourceField
        long countSourceFields = dataMappingConfigs
                .stream()
                .map(DataMappingConfig::getSourceField)
                .filter(StringUtils::isNotBlank)
                .count();
        if (countSourceFields != dataMappingConfigs.size()) {
            throw new RuntimeException("导入配置存在未填写的sourceField！");
        }
        //判断是否存在重复的serviceId和sourceField 组合
        long count = dataMappingConfigs
                .stream()
                .map(dataMappingConfig -> StringUtils.join(dataMappingConfig.getServiceId(),
                        "_",
                        dataMappingConfig.getType(),
                        "_",
                        dataMappingConfig.getSourceField()))
                .distinct().count();
        if (count != dataMappingConfigs.size()) {
            throw new RuntimeException("导入配置存在重复的serviceId,type和sourceField组合！");
        }
        // 初始化id
        dataMappingConfigs.parallelStream().forEach(dataMappingConfig -> {
            if (StringUtils.isBlank(dataMappingConfig.getId())) {
                dataMappingConfig.setId(StringUtils.join(dataMappingConfig.getServiceId(),
                        "_",
                        dataMappingConfig.getType(),
                        "_",
                        dataMappingConfig.getSourceField()));
            }
        });
    }

    /**
     * 刷新指定服务ID列表的缓存
     *
     * @param keys 服务ID列表
     * @return 实际刷新成功的数量
     */
    private int refreshCacheForServiceIds(List<String> keys) {
        if (CollectionUtils.isEmpty(keys)) {
            log.debug("没有需要刷新的缓存");
            return 0;
        }

        log.info("开始刷新{}个服务的缓存: {}", keys.size(), keys);

        int successCount = 0;
        int failCount = 0;

        for (String key : keys) {
            try {
                // 刷新缓存
                DataMappingConfigCache.refresh(key);
                successCount++;
                log.debug("成功刷新缓存, serviceId: {}", key);
            } catch (Exception e) {
                failCount++;
                log.error("刷新缓存失败, serviceId: {}", key, e);
                // 如果刷新失败，尝试移除缓存，让下次访问时重新加载
                try {
                    DataMappingConfigCache.remove(key);
                    log.debug("移除缓存成功, serviceId: {}", key);
                } catch (Exception removeEx) {
                    log.error("移除缓存也失败, serviceId: {}", key, removeEx);
                }
            }
        }

        log.info("缓存刷新完成: 成功{}个, 失败{}个", successCount, failCount);
        return successCount;
    }

    /**
     * 根据服务ID获取同步配置映射
     *
     * @param serviceId 服务ID
     * @return 同步配置映射
     */
    @Override
    public Map<String, DataMappingConfig> getDataMappingConfigMapByServiceId(String serviceId,String  type) {
        List<DataMappingConfig> configs = dataMappingConfigRepository.findByServiceIdAndType(serviceId,type);
        if (CollectionUtils.isEmpty(configs)) {
            return Map.of();
        }
        Map<String, DataMappingConfig> result = Maps.newHashMap();
        configs.forEach(config -> {
            result.put(config.getSourceField(), config);
        });
        return result;
    }

    @Override
    public String processData(Object data, Map<String, DataMappingConfig> configMap) {
        if (data == null) {
            log.debug("数据为空，跳过处理");
            return null;
        }

        try {
            // 安全转换为JsonNode
            JsonNode dataNode = safeConvertToJsonNode(data);
            if (dataNode == null || dataNode.isNull()) {
                log.debug("数据转换为JsonNode后为空，跳过处理");
                return null;
            }

            // 转换为节点列表
            List<JsonNode> nodes = convertPayloadToNodeList(dataNode);
            if (CollectionUtils.isEmpty(nodes)) {
                log.debug("数据节点列表为空，跳过处理");
                return null;
            }

            // 执行数据转换
            List<Map<String, Object>> dataMaps = dataConvert(configMap, nodes);
            if (CollectionUtils.isEmpty(dataMaps)) {
                log.debug("数据转换后为空，跳过处理");
                return null;
            }

            // 检查是否所有targetField都是List开头，决定返回格式
            boolean allTargetFieldsAreList = areAllTargetFieldsList(configMap);
            if (allTargetFieldsAreList) {
                log.debug("所有targetField都是List开头，返回数组格式JSON");
                // 如果所有targetField都是List开头，需要将结果转换为数组格式
                return convertToArrayFormat(dataMaps);
            } else {
                log.debug("存在非List开头的targetField，返回标准格式JSON");
                // 转换为目标对象并设置默认值
                return JsonUtils.toJson(dataMaps);
            }

        } catch (Exception e) {
            log.error("处理{}数据时发生异常", e);
            return null;
        }
    }

    /**
     * 安全转换对象为JsonNode
     */
    protected JsonNode safeConvertToJsonNode(Object data) {
        try {
            if (data instanceof JsonNode) {
                return (JsonNode) data;
            }
            if(data instanceof String){
                return JsonUtils.toJsonNode((String) data);
            }
            return JsonUtils.toJsonNode(JsonUtils.toJson(data));
        } catch (Exception e) {
            log.error("转换对象为JsonNode失败: {}", data, e);
            return null;
        }
    }

    /**
     * 将payload转换为List<JsonNode>
     *
     * @param payload 原始payload数据
     * @return JsonNode列表
     */
    protected List<JsonNode> convertPayloadToNodeList(JsonNode payload) {
        List<JsonNode> nodes = Lists.newArrayList();

        if (payload == null || payload.isNull()) {
            log.warn("Payload为空，返回空列表");
            return nodes;
        }

        if (payload.isArray()) {
            // 如果payload是数组，遍历每个元素
            for (JsonNode node : payload) {
                nodes.add(node);
            }
            log.debug("Payload是数组类型，包含{}个元素", nodes.size());
        } else {
            // 如果payload是单个对象，直接添加到列表中
            nodes.add(payload);
            log.debug("Payload是对象类型，转换为单元素列表");
        }

        return nodes;
    }

    /**
     * 检查configMap中的所有targetField字段是否都是List开头的
     *
     * @param configMap 配置映射
     * @return 如果所有targetField都是List开头返回true，否则返回false
     */
    private boolean areAllTargetFieldsList(Map<String, DataMappingConfig> configMap) {
        if (configMap == null || configMap.isEmpty()) {
            log.debug("配置映射为空，返回false");
            return false;
        }

        for (DataMappingConfig config : configMap.values()) {
            String targetFields = config.getTargetField();
            if (StringUtils.isBlank(targetFields)) {
                log.debug("发现空的targetField，返回false");
                return false;
            }

            // 处理逗号分隔的多个字段
            String[] targetFieldArray = targetFields.split(",");
            for (String targetField : targetFieldArray) {
                String trimmedTargetField = targetField.trim();
                if (StringUtils.isNotBlank(trimmedTargetField)) {
                    // 检查是否以"List."开头或者是"List<...>."格式
                    boolean isListFormat = trimmedTargetField.startsWith("List.") ||
                                         (trimmedTargetField.startsWith("List<") && trimmedTargetField.contains(">."));
                    if (!isListFormat) {
                        log.debug("发现非List开头的targetField: {}", trimmedTargetField);
                        return false;
                    }
                }
            }
        }

        log.debug("所有targetField都是List开头");
        return true;
    }

    /**
     * 将数据转换为数组格式的JSON字符串
     * 当所有targetField都是List开头时，需要将结果重新组织为数组格式
     *
     * @param dataMaps 转换后的数据列表
     * @return 数组格式的JSON字符串
     */
    private String convertToArrayFormat(List<Map<String, Object>> dataMaps) {
        if (CollectionUtils.isEmpty(dataMaps)) {
            return "[]";
        }

        // 收集所有List字段的数据，合并成一个统一的数组
        List<Map<String, Object>> arrayResult = Lists.newArrayList();
        int maxArraySize = 0;

        // 首先确定最大数组长度
        for (Map<String, Object> dataMap : dataMaps) {
            for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
                Object fieldValue = entry.getValue();
                if (fieldValue instanceof List) {
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> listValue = (List<Map<String, Object>>) fieldValue;
                    maxArraySize = Math.max(maxArraySize, listValue.size());
                }
            }
        }

        // 初始化结果数组
        for (int i = 0; i < maxArraySize; i++) {
            arrayResult.add(Maps.newHashMap());
        }

        // 合并所有List字段的数据
        for (Map<String, Object> dataMap : dataMaps) {
            for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
                Object fieldValue = entry.getValue();

                if (fieldValue instanceof List) {
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> listValue = (List<Map<String, Object>>) fieldValue;

                    // 将List中的每个元素的字段合并到对应的结果元素中
                    for (int i = 0; i < listValue.size(); i++) {
                        Map<String, Object> sourceElement = listValue.get(i);
                        Map<String, Object> targetElement = arrayResult.get(i);

                        // 将源元素的所有字段合并到目标元素中
                        targetElement.putAll(sourceElement);
                    }
                }
            }
        }

        log.debug("转换为数组格式，共{}个元素", arrayResult.size());
        return JsonUtils.toJson(arrayResult);
    }

    /**
     * 数据转换
     * 根据配置映射将JsonNode中的字段进行转换
     *
     * @param configMap 配置信息，key为sourceField，value为targetField（可能是逗号分隔的多个字段）
     * @param nodes     数据节点列表
     * @return 转换后的数据列表
     */
    protected List<Map<String, Object>> dataConvert(Map<String, DataMappingConfig> configMap, List<JsonNode> nodes) {
        List<Map<String, Object>> result = Lists.newArrayList();

        if (configMap == null
                || configMap.isEmpty()
                || nodes == null
                || nodes.isEmpty()) {
            log.warn("配置映射或数据节点为空，返回空结果");
            return result;
        }

        for (JsonNode node : nodes) {
            Map<String, Object> convertedData = convertSingleNode(configMap, node);
            if (!convertedData.isEmpty()) {
                result.add(convertedData);
            }
        }

        return result;
    }

    /**
     * 转换单个JsonNode
     *
     * @param configMap 配置映射
     * @param node      单个数据节点
     * @return 转换后的数据Map
     */
    private Map<String, Object> convertSingleNode(Map<String, DataMappingConfig> configMap, JsonNode node) {

        Map<String, Object> convertedData = Maps.newHashMap();

        // 遍历配置映射
        for (Map.Entry<String, DataMappingConfig> configEntry : configMap.entrySet()) {
            String sourceField = configEntry.getKey();
            DataMappingConfig config = configEntry.getValue();
            String targetFields = config.getTargetField();
            // 检查targetField是否为空
            if (StringUtils.isBlank(targetFields)) {
                log.debug("目标字段为空，跳过源字段: {}", sourceField);
                continue;
            }

            // 获取源字段的值（支持嵌套属性如a.b.c和List<a>.b格式）
            JsonNode sourceValue = getSourceFieldValue(node, sourceField);

            // 处理targetField（可能是逗号分隔的多个字段）
            String[] targetFieldArray = targetFields.split(",");
            for (String targetField : targetFieldArray) {
                String trimmedTargetField = targetField.trim();
                if (StringUtils.isNotBlank(trimmedTargetField)) {
                    // 检查是否为List格式（List<fieldName>.nestedPath 或 List.fieldName）
                    boolean isComplexListFormat = trimmedTargetField.startsWith("List<") && trimmedTargetField.contains(">.");
                    boolean isSimpleListFormat = trimmedTargetField.startsWith("List.");

                    if (isComplexListFormat || isSimpleListFormat) {
                        // 对于List类型，需要传递原始的JsonNode进行处理
                        if (sourceValue == null || sourceValue.isNull()) {
                            if (config.getDefaultValue() != null) {
                                // 如果有默认值，创建一个包含默认值的JsonNode
                                JsonNode defaultValueNode = JsonUtils.toJsonNode(JsonUtils.toJson(config.getDefaultValue()));
                                handleListTargetField(convertedData, trimmedTargetField, defaultValueNode);
                            } else {
                                log.debug("JsonNode中不包含源字段且无默认值: {}", sourceField);
                            }
                        } else {
                            handleListTargetField(convertedData, trimmedTargetField, sourceValue);
                        }
                    } else {
                        // 对于非List类型，使用提取后的值
                        Object value;
                        if (sourceValue == null || sourceValue.isNull()) {
                            if (Objects.isNull(config.getDefaultValue())) {
                                log.debug("JsonNode中不包含源字段或字段值为null: {}", sourceField);
                                continue;
                            }
                            value = config.getDefaultValue();
                        } else {
                            value = extractValue(sourceValue);
                        }

                        if (trimmedTargetField.contains(".")) {
                            // 检查是否为嵌套属性（包含点号）
                            setNestedFieldValue(convertedData, trimmedTargetField, value);
                        } else {
                            convertedData.put(trimmedTargetField, value);
                        }
                    }
                    log.debug("字段转换: {} -> {}", sourceField, trimmedTargetField);
                }
            }
        }

        return convertedData;
    }

    /**
     * 获取源字段的值，支持普通嵌套格式(a.b.c)和List格式(List<a>.b)
     *
     * @param node        JsonNode节点
     * @param sourceField 源字段路径
     * @return 字段值的JsonNode，如果不存在则返回null
     */
    private JsonNode getSourceFieldValue(JsonNode node, String sourceField) {
        if (node == null || StringUtils.isBlank(sourceField)) {
            return null;
        }

        // 检查是否为List<fieldName>.nestedPath格式
        if (sourceField.startsWith("List<") && sourceField.contains(">.")) {
            return getListSourceFieldValue(node, sourceField);
        } else {
            // 普通嵌套字段格式
            return getNestedFieldValue(node, sourceField);
        }
    }

    /**
     * 处理List<fieldName>.nestedPath格式的源字段
     *
     * @param node        JsonNode节点
     * @param sourceField 源字段路径，格式为List<fieldName>.nestedPath
     * @return 提取的JsonNode数组，包含所有匹配的值
     */
    private JsonNode getListSourceFieldValue(JsonNode node, String sourceField) {
        if (node == null || StringUtils.isBlank(sourceField)) {
            return null;
        }

        // 解析List<fieldName>.nestedPath格式
        int listStart = sourceField.indexOf("List<");
        int listEnd = sourceField.indexOf(">");
        int dotIndex = sourceField.indexOf(".", listEnd);

        if (listStart != 0 || listEnd == -1 || dotIndex == -1) {
            log.warn("源字段格式不正确，应为List<fieldName>.nestedPath格式: {}", sourceField);
            return null;
        }

        String listFieldName = sourceField.substring(5, listEnd); // 提取fieldName
        String nestedPath = sourceField.substring(dotIndex + 1); // 提取nestedPath

        log.debug("解析List源字段 - 列表字段名: {}, 嵌套路径: {}", listFieldName, nestedPath);

        // 获取列表字段
        JsonNode listNode = node.has(listFieldName) ? node.get(listFieldName) : null;
        if (listNode == null || listNode.isNull()) {
            log.debug("找不到列表字段: {}", listFieldName);
            return null;
        }

        // 创建结果数组
        ArrayNode resultArray = JsonNodeFactory.instance.arrayNode();

        if (listNode.isArray()) {
            // 如果是数组，遍历每个元素并提取嵌套路径的值
            for (JsonNode arrayElement : listNode) {
                JsonNode nestedValue = getNestedFieldValue(arrayElement, nestedPath);
                if (nestedValue != null && !nestedValue.isNull()) {
                    resultArray.add(nestedValue);
                }
            }
        } else {
            // 如果不是数组，直接提取嵌套路径的值
            JsonNode nestedValue = getNestedFieldValue(listNode, nestedPath);
            if (nestedValue != null && !nestedValue.isNull()) {
                resultArray.add(nestedValue);
            }
        }

        log.debug("从List源字段提取了{}个值", resultArray.size());
        return resultArray.size() > 0 ? resultArray : null;
    }

    /**
     * 获取嵌套字段的值，支持a.b.c格式的字段路径
     *
     * @param node      JsonNode节点
     * @param fieldPath 字段路径，支持嵌套格式如"a.b.c"
     * @return 字段值的JsonNode，如果不存在则返回null
     */
    private JsonNode getNestedFieldValue(JsonNode node, String fieldPath) {
        if (node == null || StringUtils.isBlank(fieldPath)) {
            return null;
        }

        // 如果字段路径不包含点号，直接获取字段值
        if (!fieldPath.contains(".")) {
            return node.has(fieldPath) ? node.get(fieldPath) : null;
        }

        // 处理嵌套字段路径
        String[] fieldParts = fieldPath.split("\\.");
        JsonNode currentNode = node;

        for (String fieldPart : fieldParts) {
            if (currentNode == null || !currentNode.has(fieldPart)) {
                log.debug("嵌套字段路径中断，找不到字段: {} 在路径: {}", fieldPart, fieldPath);
                return null;
            }
            currentNode = currentNode.get(fieldPart);
        }

        log.debug("成功获取嵌套字段值: {}", fieldPath);
        return currentNode;
    }

    /**
     * 处理List<fieldName>.nestedPath格式的目标字段
     *
     * @param dataMap     目标数据Map
     * @param targetField 目标字段，格式为List<fieldName>.nestedPath
     * @param sourceValue 源JsonNode值
     */
    private void handleListTargetField(Map<String, Object> dataMap, String targetField, JsonNode sourceValue) {
        if (dataMap == null || StringUtils.isBlank(targetField)) {
            log.warn("数据Map或目标字段为空，无法处理List类型目标字段");
            return;
        }

        if (sourceValue == null || sourceValue.isNull()) {
            log.debug("源JsonNode为空，跳过List类型目标字段处理: {}", targetField);
            return;
        }

        String listFieldName;
        String nestedPath;

        // 检查是简单List.fieldName格式还是复杂List<fieldName>.nestedPath格式
        if (targetField.startsWith("List.")) {
            // 简单格式：List.fieldName
            int dotIndex = targetField.indexOf(".");
            if (dotIndex == -1 || dotIndex == targetField.length() - 1) {
                log.warn("目标字段格式不正确，应为List.fieldName格式: {}", targetField);
                return;
            }
            listFieldName = "items"; // 默认使用items作为列表字段名
            nestedPath = targetField.substring(dotIndex + 1); // 提取fieldName
            log.debug("解析简单List目标字段 - 列表字段名: {}, 字段名: {}", listFieldName, nestedPath);
        } else {
            // 复杂格式：List<fieldName>.nestedPath
            int listStart = targetField.indexOf("List<");
            int listEnd = targetField.indexOf(">");
            int dotIndex = targetField.indexOf(".", listEnd);

            if (listStart != 0 || listEnd == -1 || dotIndex == -1) {
                log.warn("目标字段格式不正确，应为List<fieldName>.nestedPath格式: {}", targetField);
                return;
            }

            listFieldName = targetField.substring(5, listEnd); // 提取fieldName
            nestedPath = targetField.substring(dotIndex + 1); // 提取nestedPath
            log.debug("解析复杂List目标字段 - 列表字段名: {}, 嵌套路径: {}", listFieldName, nestedPath);
        }

        // 获取或创建列表
        List<Map<String, Object>> targetList = getOrCreateList(dataMap, listFieldName);

        // 处理源值 - 检查是否为数组
        if (sourceValue.isArray()) {
            log.debug("源值是数组，包含{}个元素", sourceValue.size());
            // 如果源值是数组，循环处理每个元素
            for (int i = 0; i < sourceValue.size(); i++) {
                JsonNode arrayElement = sourceValue.get(i);
                Object elementValue = extractValue(arrayElement);

                // 确保列表有足够的元素
                while (targetList.size() <= i) {
                    targetList.add(Maps.newHashMap());
                }

                // 设置嵌套属性
                Map<String, Object> targetElement = targetList.get(i);
                if (nestedPath.contains(".")) {
                    setNestedFieldValue(targetElement, nestedPath, elementValue);
                } else {
                    targetElement.put(nestedPath, elementValue);
                }
                log.debug("设置数组元素[{}]: {} = {}", i, nestedPath, elementValue);
            }
        } else {
            log.debug("源值不是数组，作为单个元素处理");
            // 如果源值不是数组，将其作为单个元素处理
            Object elementValue = extractValue(sourceValue);

            // 确保列表至少有一个元素
            if (targetList.isEmpty()) {
                targetList.add(Maps.newHashMap());
            }

            // 设置嵌套属性到第一个元素
//            Map<String, Object> targetElement = targetList.get(0);
            targetList.stream().forEach(targetElement->{
                if (nestedPath.contains(".")) {
                    setNestedFieldValue(targetElement, nestedPath, elementValue);
                } else {
                    targetElement.put(nestedPath, elementValue);
                }
            });
            log.debug("设置单个元素: {} = {}", nestedPath, elementValue);
        }

        log.debug("成功处理List类型目标字段: {} -> 列表大小: {}", targetField, targetList.size());
    }

    /**
     * 获取或创建指定名称的列表
     *
     * @param dataMap       目标数据Map
     * @param listFieldName 列表字段名
     * @return 列表对象
     */
    @SuppressWarnings("unchecked")
    private List<Map<String, Object>> getOrCreateList(Map<String, Object> dataMap, String listFieldName) {
        Object existingValue = dataMap.get(listFieldName);

        if (existingValue instanceof List) {
            // 检查列表元素类型
            List<?> existingList = (List<?>) existingValue;
            if (existingList.isEmpty() || existingList.get(0) instanceof Map) {
                return (List<Map<String, Object>>) existingList;
            }
        }

        // 创建新的列表
        List<Map<String, Object>> newList = Lists.newArrayList();
        dataMap.put(listFieldName, newList);
        return newList;
    }

    /**
     * 设置嵌套字段的值，支持a.b.c格式的字段路径
     *
     * @param dataMap   目标数据Map
     * @param fieldPath 字段路径，支持嵌套格式如"a.b.c"
     * @param value     要设置的值
     */
    @SuppressWarnings("unchecked")
    private void setNestedFieldValue(Map<String, Object> dataMap, String fieldPath, Object value) {
        if (dataMap == null || StringUtils.isBlank(fieldPath)) {
            log.warn("数据Map或字段路径为空，无法设置嵌套字段值");
            return;
        }

        // 如果字段路径不包含点号，直接设置字段值
        if (!fieldPath.contains(".")) {
            dataMap.put(fieldPath, value);
            return;
        }

        // 处理嵌套字段路径
        String[] fieldParts = fieldPath.split("\\.");
        Map<String, Object> currentMap = dataMap;

        // 遍历到倒数第二个字段，确保路径上的所有Map都存在
        for (int i = 0; i < fieldParts.length - 1; i++) {
            String fieldPart = fieldParts[i];

            // 如果当前字段不存在，创建一个新的Map
            if (!currentMap.containsKey(fieldPart)) {
                currentMap.put(fieldPart, Maps.newHashMap());
            }

            // 获取下一级Map
            Object nextLevel = currentMap.get(fieldPart);
            if (!(nextLevel instanceof Map)) {
                // 如果不是Map类型，创建一个新的Map替换它
                log.warn("嵌套字段路径中存在非Map类型的值，将被替换: {} 在路径: {}", fieldPart, fieldPath);
                nextLevel = Maps.newHashMap();
                currentMap.put(fieldPart, nextLevel);
            }

            currentMap = (Map<String, Object>) nextLevel;
        }

        // 设置最后一个字段的值
        String lastField = fieldParts[fieldParts.length - 1];
        currentMap.put(lastField, value);

        log.debug("成功设置嵌套字段值: {} = {}", fieldPath, value);
    }

    /**
     * 从JsonNode中提取值
     *
     * @param jsonNode JsonNode节点
     * @return 提取的值
     */
    private Object extractValue(JsonNode jsonNode) {
        if (jsonNode == null || jsonNode.isNull()) {
            return null;
        }

        if (jsonNode.isBoolean()) {
            return jsonNode.booleanValue();
        } else if (jsonNode.isInt()) {
            return jsonNode.intValue();
        } else if (jsonNode.isLong()) {
            return jsonNode.longValue();
        } else if (jsonNode.isDouble()) {
            return jsonNode.doubleValue();
        } else if (jsonNode.isFloat()) {
            return jsonNode.floatValue();
        } else if (jsonNode.isTextual()) {
            return jsonNode.textValue();
        } else if (jsonNode.isArray()) {
            // 处理数组类型
            List<Object> arrayList = Lists.newArrayList();
            for (JsonNode element : jsonNode) {
                arrayList.add(extractValue(element));
            }
            return arrayList;
        } else if (jsonNode.isObject()) {
            // 处理对象类型
            Map<String, Object> objectMap = Maps.newHashMap();
            Iterator<Map.Entry<String, JsonNode>> fields = jsonNode.fields();
            while (fields.hasNext()) {
                Map.Entry<String, JsonNode> field = fields.next();
                objectMap.put(field.getKey(), extractValue(field.getValue()));
            }
            return objectMap;
        } else {
            // 其他类型，转换为字符串
            return jsonNode.toString();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importExcel(InputStream inputStream) {
        ExcelUtil<DataMappingConfig> util = new ExcelUtil<>(DataMappingConfig.class);
        List<DataMappingConfig> dataMappingConfigs = util.importExcel(inputStream);
        return importSyncConfig(dataMappingConfigs);
    }
}

