package bici.bzlj.dataprocess.decode.common;

import bici.bzlj.dataprocess.core.event.MessageEvent;
import bici.bzlj.dataprocess.core.handler.IMessageHandler;
import bici.bzlj.dataprocess.core.model.DataFlowStatus;
import bici.bzlj.dataprocess.decode.dataConvert.cache.DataMappingConfigCache;
import bici.bzlj.dataprocess.decode.dataConvert.entity.DataMappingConfig;
import bici.bzlj.dataprocess.decode.dataConvert.service.IDataMappingConfigService;
import io.jsonwebtoken.lang.Collections;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.function.StreamBridge;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-06-06 10:42
 */
@Slf4j
public abstract class ForwardHandler<T> implements IMessageHandler<T> {
    public String desc;

    @Autowired
    public IDataMappingConfigService dataMappingConfigService;

    final ZoneOffset offset = ZoneOffset.UTC;

    public final StreamBridge streamBridge;

    private final String GYCK_CRAFT = "gyck_craft";

    protected ForwardHandler(StreamBridge streamBridge) {
        this.streamBridge = streamBridge;
    }

    public abstract String getGyckServiceId();


    /**
     * 消息处理前
     *
     * @param event         消息事件
     * @param currentHandleContext 处理上下文
     */
    @Override
    public void handlePre(MessageEvent<T> event, Map<String, Object> currentHandleContext, Map<String, Object> handlesContext) {
        currentHandleContext.put("startTime", LocalDateTime.now());
        String gyckServiceId = getGyckServiceId();
        Map<String, DataMappingConfig> configMap = DataMappingConfigCache.get(String.format("%s__%s",event.getMessageType(),gyckServiceId));
        if (!Collections.isEmpty(configMap)) {
            String maps = dataMappingConfigService.processData(event.getPayload(), configMap);
            event.setConvertData(maps);
        }
        log.info("{}消息{}开始处理", getImplDesc(event.getMessageType()), event.getMessageId());
    }


    @Override
    public void handlePost(MessageEvent<T> event, Map<String, Object> currentHandleContext, Map<String, Object> handlesContext){
        LocalDateTime startTime = (LocalDateTime) currentHandleContext.get("startTime");
        long startMillis = startTime.atOffset(offset).toInstant().toEpochMilli();
        long endMillis = LocalDateTime.now().atOffset(offset).toInstant().toEpochMilli();
        String serviceId = (String)currentHandleContext.get("serviceId");
        ForwardMessageInfo<Object> forwardMessage = new ForwardMessageInfo<>();
        forwardMessage.setServiceId(serviceId);
        forwardMessage.setSendTime(endMillis);
        forwardMessage.setPayload(currentHandleContext.get(serviceId));
        forwardMessage.setDataFlowStatus(DataFlowStatus.external);
        forwardMessage.setTelegramId(String.format("%s_%s",String.format("%s__%s",event.getMessageType(),serviceId),event.getMessageId()));
        streamBridge.send(GYCK_CRAFT, forwardMessage);

        log.info("{}消息{}处理完成,耗时:{}ms", getImplDesc(event.getMessageType()),
                event.getMessageId(), endMillis - startMillis);
    }

    public String getImplDesc(String messageType) {
        if (StringUtils.isNotBlank(desc)) {
            return desc;
        }
        this.desc = getDesc(messageType);
        return desc;
    }

    /**
     * 错误处理
     *
     * @param event         错误消息事件
     * @param currentHandleContext 处理上下文
     * @param e             错误信息
     */
    @Override
    public void handleError(MessageEvent<T> event, Map<String, Object> currentHandleContext, Map<String, Object> handlesContext, Exception e) {
        log.error("{}消息{}处理异常:{}", getImplDesc(event.getMessageType()), event.getMessageId(), e.getMessage(), e);
    }
}
