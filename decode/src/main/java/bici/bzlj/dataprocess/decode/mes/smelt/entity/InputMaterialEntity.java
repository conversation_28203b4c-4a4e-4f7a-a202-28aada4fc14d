package bici.bzlj.dataprocess.decode.mes.smelt.entity;

import bici.bzlj.dataprocess.decode.annotation.ImplantAttr;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * DYMTN2
 * 投入物料实绩电文
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class InputMaterialEntity {

    /**
     * 制造命令号/计划号
     */
    @JsonProperty("PONO")
    @JsonAlias("PLAN_NO")
    private String planNo;

    /**
     * 熔炼号
     */
    @JsonProperty("HEAT_NO")
    private String heatNo;

    /**
     * 入口材料号
     */
    @JsonProperty("IN_MAT_NO")
    private String inMatNo;

    /**
     * 入口材料支数
     */
    @JsonProperty("MAT_NUM_IN")
    @ImplantAttr(value = "physicalAttr",alias = "quantity")
    private BigDecimal matNumIn;

    /**
     * 入口锭坯型
     */
    @JsonProperty("PROFILE_ID_IN")
    @ImplantAttr(value = "specificationAttr", alias = "ingotType")
    private String profileIdIn;

    /**
     * 材料重量
     */
    @JsonProperty("IN_MAT_WT")
    @ImplantAttr(value = "physicalAttr",alias = "weight")
    private BigDecimal inMatWt;

}
