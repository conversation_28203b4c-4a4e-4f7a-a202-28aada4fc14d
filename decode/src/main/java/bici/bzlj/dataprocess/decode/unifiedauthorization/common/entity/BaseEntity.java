package bici.bzlj.dataprocess.decode.unifiedauthorization.common.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2025/6/17 11:22
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BaseEntity {
    @JsonProperty("telegramId")
    private String telegramId;
}
