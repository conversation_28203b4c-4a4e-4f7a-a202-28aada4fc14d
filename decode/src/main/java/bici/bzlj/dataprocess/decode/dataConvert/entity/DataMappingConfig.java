package bici.bzlj.dataprocess.decode.dataConvert.entity;

import bici.bzlj.dataprocess.decode.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2025/7/2 14:27
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@Accessors(chain = true)
@Document(collection = "data_mapping_config")
@Data
public class DataMappingConfig  implements Serializable {

    @Excel(name = "ID")
    @Id
    private String id;

    @Excel(name = "服务ID")
    private String serviceId;

    @Excel(name = "电文类型")
    private String type;

    /**
     * 来源字段
     */
    @Excel(name = "来源字段")
    private String sourceField;

    /**
     * 目标字段
     */
    @Excel(name = "目标字段")
    private String targetField;

    @Excel(name = "默认值")
    private Object defaultValue;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @CreatedDate
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @LastModifiedDate
    private Date updateTime;

}
