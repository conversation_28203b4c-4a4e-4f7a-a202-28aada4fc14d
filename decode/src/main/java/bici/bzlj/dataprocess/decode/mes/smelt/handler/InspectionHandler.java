package bici.bzlj.dataprocess.decode.mes.smelt.handler;

import bici.bzlj.dataprocess.core.annotation.MessageHandler;
import bici.bzlj.dataprocess.core.event.MessageEvent;
import bici.bzlj.dataprocess.decode.common.ForwardHandler;
import bici.bzlj.dataprocess.decode.mes.smelt.convert.InspectionData;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.stream.function.StreamBridge;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;


/**
 * TODO
 *  检化验数据
 *
 * <AUTHOR>
 * @date 2025/5/15 16:17
 */
@MessageHandler(messageType = {"R0MT62",
        "R0MT04", "R0MT05", "R0MT06", "R0MT07", "R0MT08", "R0MT09", "R0MT10", "R0MT11", "R0MT12", "R0MT13", "R0MT14", "R0MT15", "R0MT16", "R0MT17", "R0MT18", "R0MT19", "R0MT20", "R0MT21", "R0MT22", "R0MT23", "R0MT24", "R0MT25", "R0MT26", "R0MT27", "R0MT28", "R0MT29", "R0MT30", "R0MT31", "R0MT32", "R0MT33", "R0MT34",
        "R0MT45", "R0MT46", "R0MT47", "R0MT48", "R0MT49", "R0MT50", "R0MT51", "R0MT52",
        "R0MT35", "R0MT36", "R0MT37", "R0MT38", "R0MT39", "R0MT40", "R0MT41", "R0MT42", "R0MT43", "R0MT44"
}, desc = "检化验数据")
@Slf4j
public class InspectionHandler extends ForwardHandler<Map<String,Object>> {

    private static final String SERVICE_ID = "check_data";

    @Override
    public String getGyckServiceId() {
        return SERVICE_ID;
    }

    protected InspectionHandler(StreamBridge streamBridge) {
        super(streamBridge);
    }

    /**
     * 消息处理
     *
     * @param event         消息事件
     * @param currentHandleContext 处理上下文
     */
    @Override
    public void handle(MessageEvent<Map<String,Object>> event, Map<String, Object> currentHandleContext, Map<String, Object> handlesContext) {
        Map<String,Object> payload = event.getPayload();
        InspectionData data = parse(payload, event.getMessageId());
        currentHandleContext.put("serviceId", SERVICE_ID);
        currentHandleContext.put(SERVICE_ID, data);
    }

    private String getDataType(String messageId){
        return switch (messageId) {
            case "R0MT62" -> "熔炼化学成分";
            case "R0MT04", "R0MT05", "R0MT06", "R0MT07", "R0MT08", "R0MT09", "R0MT10", "R0MT11", "R0MT12", "R0MT13", "R0MT14", "R0MT15", "R0MT16", "R0MT17", "R0MT18", "R0MT19", "R0MT20", "R0MT21", "R0MT22", "R0MT23", "R0MT24", "R0MT25", "R0MT26", "R0MT27", "R0MT28", "R0MT29", "R0MT30", "R0MT31", "R0MT32", "R0MT33", "R0MT34",
                 "R0MT45", "R0MT46", "R0MT47", "R0MT48", "R0MT49", "R0MT50", "R0MT51", "R0MT52" -> "理化检验1";
            case "R0MT35", "R0MT36", "R0MT37", "R0MT38", "R0MT39", "R0MT40", "R0MT41", "R0MT42", "R0MT43", "R0MT44" -> "理化检验2";
            default -> null;
        };
    }

    private int getExtraStartIndex(String dataType) {
        return switch (dataType) {
            case "熔炼化学成分", "理化检验1" -> 18;
            case "理化检验2" -> 22;
            default -> 1; // 默认不处理额外字段
        };
    }

    private String getRuleName(String messageId) {
        //todo 获取检化验名称
        return "";
    }

    public InspectionData parse(Map<String,Object> payload,String messageId) {

        // 1. 获取字段映射配置
        Map<String, String> mapping = new HashMap<>();


        // 3. 创建统一实体
        InspectionData entity = new InspectionData();
        entity.setRuleCode(messageId);
        entity.setRuleName(getRuleName(messageId));
        Map<String, Object> extraFields = new LinkedHashMap<>();

        // 4. 计算额外字段起始索引
        int extraStartIndex = getExtraStartIndex(getDataType(messageId));

        // 5. 遍历映射配置处理字段
        int index = 1;
        for (Map.Entry<String, String> entry : mapping.entrySet()) {
            String enName = entry.getKey();
            String desc = entry.getValue();
            Object value = payload.get(enName);

            // 5.1 映射到实体字段,试样号是新加的，在电文结尾
            if (index < extraStartIndex || "试样号".equals(desc)) {
                setFieldByChineseName(entity, desc, value);
            }
            // 5.2 处理额外字段
            else {
                extraFields.put(enName + "," + desc, value);
            }

            index++;
        }

        entity.setExtraFields(extraFields);
        return entity;
    }

    private void setFieldByChineseName(InspectionData entity, String desc, Object value) {
        try {
            for (Field field : entity.getClass().getDeclaredFields()) {
                JsonProperty annotation = field.getAnnotation(JsonProperty.class);
                if (annotation != null && annotation.value().equals(desc)) {
                    field.setAccessible(true);
                    field.set(entity, value);
                    break;
                }
            }
        } catch (IllegalAccessException e) {
            // 处理异常或记录日志
        }
    }

}
