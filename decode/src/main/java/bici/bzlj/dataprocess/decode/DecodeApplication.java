package bici.bzlj.dataprocess.decode;

import com.bzlj.dynamic.mongo.annotations.EnableDynamicMongo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.data.mongodb.config.EnableMongoAuditing;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2025/5/14 10:32
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@SpringBootApplication
@EnableDynamicMongo
@EnableMongoAuditing
@EnableMongoRepositories(basePackages = {"bici.bzlj.dataprocess.decode.dataConvert.repository"})
public class DecodeApplication {
    public static void main(String[] args) {
        SpringApplication.run(DecodeApplication.class, args);
    }
}
