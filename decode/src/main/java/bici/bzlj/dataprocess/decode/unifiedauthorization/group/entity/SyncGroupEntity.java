package bici.bzlj.dataprocess.decode.unifiedauthorization.group.entity;

import bici.bzlj.dataprocess.decode.unifiedauthorization.common.entity.BaseEntity;
import bici.bzlj.dataprocess.decode.unifiedauthorization.common.entity.Block;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 群组维护同步消息
 *
 * <AUTHOR>
 * @date 2025/6/18 09:49
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SyncGroupEntity extends BaseEntity {
    @JsonProperty("table1")
    private Block table1;
    @JsonProperty("INSERT_BLOCK")
    private Block insertBlock;
    @JsonProperty("UPDATE_BLOCK")
    private Block updateBlock;
    @JsonProperty("DELETE_BLOCK")
    private Block deleteBlock;
}
