package bici.bzlj.dataprocess.decode.unifiedauthorization.common.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class Column {
    @JsonProperty("pos")
    private int pos;
    @JsonProperty("name")
    private String name;
    @JsonProperty("desc_name")
    private String descName;
    @JsonProperty("type")
    private String type;
}