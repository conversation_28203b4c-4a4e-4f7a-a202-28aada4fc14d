package bici.bzlj.dataprocess.decode.mes.smelt.handler.finishing;

import bici.bzlj.dataprocess.core.annotation.MessageHandler;
import bici.bzlj.dataprocess.core.event.MessageEvent;
import bici.bzlj.dataprocess.core.utils.JsonUtils;
import bici.bzlj.dataprocess.decode.common.ForwardHandler;
import bici.bzlj.dataprocess.decode.mes.smelt.handler.finishing.common.DealJsonNode;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.stream.function.StreamBridge;

import java.util.Map;


/**
 * TODO
 *  特冶精整投入物料电文监听
 *
 * <AUTHOR>
 * @date 2025/5/15 16:17
 */
@MessageHandler(messageType = {"DXMTR2"}, desc = "特冶精整投入物料电文监听")
@Slf4j
public class SmeltFinishingProductionPerformanceHandler extends ForwardHandler<String> {

    protected SmeltFinishingProductionPerformanceHandler(StreamBridge streamBridge) {
        super(streamBridge);
    }

    @Override
    public String getGyckServiceId() {
        return SERVICE_ID;
    }

    private static final String SERVICE_ID = "production_performance";

    /**
     * 消息处理
     *
     * @param event                消息事件
     * @param currentHandleContext 处理上下文
     */
    @Override
    public void handle(MessageEvent<String> event, Map<String, Object> currentHandleContext, Map<String, Object> handlesContext) {

        // 将处理结果放入上下文
        currentHandleContext.put("serviceId", SERVICE_ID);
        currentHandleContext.put(SERVICE_ID, event.getConvertData());
    }


}
