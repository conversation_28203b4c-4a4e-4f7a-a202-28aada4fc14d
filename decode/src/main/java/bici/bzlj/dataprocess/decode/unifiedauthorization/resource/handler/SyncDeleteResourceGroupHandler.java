package bici.bzlj.dataprocess.decode.unifiedauthorization.resource.handler;

import bici.bzlj.dataprocess.core.annotation.MessageHandler;
import bici.bzlj.dataprocess.core.event.MessageEvent;
import bici.bzlj.dataprocess.decode.unifiedauthorization.common.entity.Block;
import bici.bzlj.dataprocess.decode.unifiedauthorization.common.handler.AbsUnifiedAuthorizationHandler;
import bici.bzlj.dataprocess.decode.unifiedauthorization.resource.entity.SyncDeleteResourceGroupEntity;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.stream.function.StreamBridge;

import java.util.List;
import java.util.Map;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2025/6/18 15:13
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@MessageHandler(messageType = "R_BE_ES_22", desc = "资源群组删除资源同步消息(R_BE_ES_22)")
@Slf4j
public class SyncDeleteResourceGroupHandler extends AbsUnifiedAuthorizationHandler<SyncDeleteResourceGroupEntity> {
    protected SyncDeleteResourceGroupHandler(StreamBridge streamBridge) {
        super(streamBridge);
    }



    /**
     * 消息处理
     *
     * @param event                消息事件
     * @param currentHandleContext 当前handle上下文
     * @param handlesContext       当前handle组上下文
     */
    @Override
    public void handle(MessageEvent<SyncDeleteResourceGroupEntity> event,
                       Map<String, Object> currentHandleContext,
                       Map<String, Object> handlesContext) {
        SyncDeleteResourceGroupEntity payload = event.getPayload();
        Block table1 = payload.getTable1();
        List<JsonNode> groupInfos = processBlockData(table1);
        Map<String, Object> result = Map.of("table1", dataConvert(groupInfos));
        currentHandleContext.put("forward_topic", "unified_authorization");
        currentHandleContext.put(payload.getTelegramId(), result);

    }
}
