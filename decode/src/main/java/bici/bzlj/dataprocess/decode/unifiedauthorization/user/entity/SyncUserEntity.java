package bici.bzlj.dataprocess.decode.unifiedauthorization.user.entity;

import bici.bzlj.dataprocess.decode.unifiedauthorization.common.entity.BaseEntity;
import bici.bzlj.dataprocess.decode.unifiedauthorization.common.entity.Block;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * R_BE_ES_15 用户同步消息实体
 *
 * <AUTHOR>
 * @date 2025/5/16 15:57
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SyncUserEntity extends BaseEntity {
    @JsonProperty("Table1")
    private Block table1;
    @JsonProperty("UPDATE_BLOCK")
    private Block updateBlock;
    @JsonProperty("INSERT_BLOCK")
    private Block insertBlock;
}