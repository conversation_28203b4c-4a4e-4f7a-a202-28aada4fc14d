package bici.bzlj.dataprocess.decode.unifiedauthorization.group.handler;

import bici.bzlj.dataprocess.core.annotation.MessageHandler;
import bici.bzlj.dataprocess.core.event.MessageEvent;
import bici.bzlj.dataprocess.decode.unifiedauthorization.common.entity.Block;
import bici.bzlj.dataprocess.decode.unifiedauthorization.common.handler.AbsUnifiedAuthorizationHandler;
import bici.bzlj.dataprocess.decode.unifiedauthorization.group.entity.SyncGroupEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.stream.function.StreamBridge;

import java.util.List;
import java.util.Map;

/**
 * 群组维护同步消息
 *
 * <AUTHOR>
 * @date 2025/6/18 09:57
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@MessageHandler(messageType = "R_BE_ES_16", desc = "群组维护同步消息(R_BE_ES_16)")
@Slf4j
public class SyncGroupHandler extends AbsUnifiedAuthorizationHandler<SyncGroupEntity> {

    protected SyncGroupHandler(StreamBridge streamBridge) {
        super(streamBridge);
    }


    /**
     * 消息处理
     *
     * @param event                消息事件
     * @param currentHandleContext 当前handle上下文
     * @param handlesContext       当前handle组上下文
     */
    @Override
    public void handle(MessageEvent<SyncGroupEntity> event,
                       Map<String, Object> currentHandleContext,
                       Map<String, Object> handlesContext) {
        SyncGroupEntity payload = event.getPayload();
        Block insertBlock = payload.getInsertBlock();
        Block updateBlock = payload.getUpdateBlock();
        Block deleteBlock = payload.getDeleteBlock();
        List<Map<String, Object>> insertData = dataConvert(processBlockData(insertBlock));
        List<Map<String, Object>> updateData = dataConvert(processBlockData(updateBlock));
        List<Map<String, Object>> deleteData = dataConvert(processBlockData(deleteBlock));
        Map<String, Object> result = Map.of("insertData", insertData,"updateData", updateData,
                "deleteData", deleteData);
        currentHandleContext.put("forward_topic", "unified_authorization");
        currentHandleContext.put(payload.getTelegramId(), result);
    }
}
