package bici.bzlj.dataprocess.decode.mes.smelt.entity;

import bici.bzlj.dataprocess.decode.annotation.ImplantAttr;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DXMTB7、DXMTB0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TaskStatusEntity {

    /**
     * 制造命令号
     */
    @JsonProperty("PONO")
    private String pono;

    /**
     * 机组设备号（炉座号）
     */
    @JsonProperty("MACHINE_NO")
    private String machineNo;

    /**
     * 02-可撤销：接收到回退请求后， 回复可撤销计划，L2同步删除该计划。MES收到该标志，对MES计划设置回退
     * 08-不可撤销：接收到回退请求后，因现场已开始生产，不可撤销计划
     */
    @JsonProperty("PLAN_STATUS")
    @ImplantAttr
    private String planStatus;

    /**
     * 生产时刻
     */
    @JsonProperty("PROD_TIME")
    private String prodTime;

    /**
     * 生产班次
     */
    @JsonProperty("PROD_SHIFT_NO")
    private String prodShiftNo;

    /**
     * 生产班组
     */
    @JsonProperty("PROD_SHIFT_GROUP")
    private String prodShiftGroup;

    /**
     * 生产责任者
     */
    @JsonProperty("PROD_MAKER")
    private String prodMaker;

    /**
     * 计划备注
     */
    @JsonProperty("REMARK_PS")
    private String remarkPs;
}
