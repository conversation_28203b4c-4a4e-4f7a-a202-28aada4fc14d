package bici.bzlj.dataprocess.decode.mes.smelt.handler;

import bici.bzlj.dataprocess.core.annotation.MessageHandler;
import bici.bzlj.dataprocess.core.event.MessageEvent;
import bici.bzlj.dataprocess.core.utils.JsonUtils;
import bici.bzlj.dataprocess.decode.util.ImplantAttrUtil;
import bici.bzlj.dataprocess.decode.common.ForwardHandler;
import bici.bzlj.dataprocess.decode.mes.smelt.convert.ProductionPerformance;
import bici.bzlj.dataprocess.decode.mes.smelt.entity.ProductionPerformanceEntity;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * TODO
 *  特冶感应炉材料产出实绩电文接收(DXMTM1)
 *
 * <AUTHOR>
 * @date 2025/5/15 16:17
 */
@MessageHandler(messageType = {"DZMTQ1"}, desc = "特冶感应炉材料产出实绩电文接收")
@Slf4j
public class ProductionPerformanceHandler extends ForwardHandler<String> {

    private static final String SERVICE_ID = "production_performance";

    @Override
    public String getGyckServiceId() {
        return SERVICE_ID;
    }

    //投入等于产出的电文
    private static final List<String> inputEqualProduction = List.of("DXMTQ1","DXMTR2");

    protected ProductionPerformanceHandler(StreamBridge streamBridge) {
        super(streamBridge);
    }

    /**
     * 消息处理
     *
     * @param event         消息事件
     * @param currentHandleContext 处理上下文
     */
    @Override
    public void handle(MessageEvent<String> event, Map<String, Object> currentHandleContext, Map<String, Object> handlesContext) {
        ProductionPerformanceEntity payload =
                JsonUtils.fromJson(Objects.requireNonNull(event.getPayload()), ProductionPerformanceEntity.class);
        ProductionPerformance productionPerformance = dealProductionPerformance(payload,event.getMessageId());
        ObjectMapper mapper = new ObjectMapper();
        try {
            String json = mapper.writeValueAsString(productionPerformance);
            currentHandleContext.put("serviceId", SERVICE_ID);
            currentHandleContext.put(SERVICE_ID, json);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

    }

    private ProductionPerformance dealProductionPerformance(ProductionPerformanceEntity payload,String messageId) {
        ProductionPerformance productionPerformance = new ProductionPerformance();
        productionPerformance.setTaskCode(payload.getPono());
        productionPerformance.setStartTime(payload.getStartProdTime());
        productionPerformance.setEndTime(payload.getEndProdTime());
        ProductionPerformance.TeamInfo teamInfo = new ProductionPerformance.TeamInfo();
        teamInfo.setStartShiftGroup(payload.getStartShiftGroup());
        teamInfo.setStartShiftNo(payload.getStartShiftNo());
        teamInfo.setStartShiftLeader(payload.getStartProdMaker());
        teamInfo.setEndShiftGroup(payload.getEndShiftGroup());
        teamInfo.setEndShiftNo(payload.getEndShiftNo());
        teamInfo.setEndShiftLeader(payload.getEndProdMaker());
        productionPerformance.setTeamInfo(teamInfo);
        if(inputEqualProduction.contains(messageId)){
            productionPerformance.setInputEQOutput(true);
        }
        List<String> inputMaterials = new ArrayList<>();
        List<ProductionPerformance.OutputMaterial> outputMaterials = new ArrayList<>();
        if (!CollectionUtils.isEmpty(payload.getLoops())) {
            payload.getLoops().forEach(loop -> {

                ProductionPerformance.OutputMaterial outputMaterial = new ProductionPerformance.OutputMaterial();
                //物料号 必须
                outputMaterial.setMaterialCode(loop.getMatNo());
                //物理属性
                Map<String, Object> physicalAttr = ImplantAttrUtil.implantConvertMap(loop, "physicalAttr");
                //规格属性
                Map<String, Object> specificationAttr = ImplantAttrUtil.implantConvertMap(loop, "specificationAttr");
                outputMaterial.setHeatNumber(payload.getHeatNo());
                outputMaterial.setSpecificationAttr(specificationAttr);
                outputMaterial.setPhysicalAttr(physicalAttr);
                outputMaterials.add(outputMaterial);
            });
        }
        productionPerformance.setInputMaterials(inputMaterials);
        productionPerformance.setOutputMaterials(outputMaterials);
        Map<String, Object> operationLog = ImplantAttrUtil.implantConvertMap(payload, "operationLog");
        productionPerformance.setOperationLog(operationLog);
        return productionPerformance;
    }

}
