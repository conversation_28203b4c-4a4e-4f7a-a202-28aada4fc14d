package bici.bzlj.dataprocess.decode.mes.titanium.smelt.handler;

import bici.bzlj.dataprocess.core.annotation.MessageHandler;
import bici.bzlj.dataprocess.core.event.MessageEvent;
import bici.bzlj.dataprocess.core.utils.JsonUtils;
import bici.bzlj.dataprocess.decode.common.ForwardHandler;
import bici.bzlj.dataprocess.decode.mes.smelt.entity.LoadMaterialEntity;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.stream.function.StreamBridge;

import java.util.Map;
import java.util.Objects;


/**
 * TODO
 *  原料消耗实绩电文
 * <AUTHOR>
 * @date 2025/5/15 16:17
 */
@MessageHandler(messageType = {"DRMTM2"}, desc = "原料消耗实绩电文")
@Slf4j
public class TitaniumLoadMaterialHandler extends ForwardHandler<String> {

    private static final String SERVICE_ID = "load_material";

    @Override
    public String getGyckServiceId() {
        return SERVICE_ID;
    }

    protected TitaniumLoadMaterialHandler(StreamBridge streamBridge) {
        super(streamBridge);
    }

    /**
     * 消息处理
     *
     * @param event         消息事件
     * @param currentHandleContext 处理上下文
     */
    @Override
    public void handle(MessageEvent<String> event, Map<String, Object> currentHandleContext, Map<String, Object> handlesContext) {
        // 将处理结果放入上下文
        currentHandleContext.put("serviceId", SERVICE_ID);
        currentHandleContext.put(SERVICE_ID, event.getConvertData());
    }

}
