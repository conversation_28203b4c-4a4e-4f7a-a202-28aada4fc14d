package bici.bzlj.dataprocess.decode.unifiedauthorization.common.consumer;

import bici.bzlj.dataprocess.decode.common.AbsCommonConsumer;
import bici.bzlj.dataprocess.decode.common.CommonMessageInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Consumer;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2025/5/15 15:06
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class UnifiedAuthorization extends AbsCommonConsumer {
    /**
     * 实时数据消费者
     */
    @Bean
    public Consumer<List<CommonMessageInfo<?>>>unifiedAuthorizationConsumer() {
        return this::handleUserInfoChangeList;
    }

    private void handleUserInfoChangeList(List<CommonMessageInfo<?>> nodes) {
        nodes.forEach(p -> {
            handleData(p, p.getType());
        });
    }
}
