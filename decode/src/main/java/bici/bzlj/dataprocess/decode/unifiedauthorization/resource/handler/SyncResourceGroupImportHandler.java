package bici.bzlj.dataprocess.decode.unifiedauthorization.resource.handler;

import bici.bzlj.dataprocess.core.annotation.MessageHandler;
import bici.bzlj.dataprocess.core.event.MessageEvent;
import bici.bzlj.dataprocess.decode.unifiedauthorization.common.entity.Block;
import bici.bzlj.dataprocess.decode.unifiedauthorization.common.handler.AbsUnifiedAuthorizationHandler;
import bici.bzlj.dataprocess.decode.unifiedauthorization.resource.entity.SyncResourceGroupImportEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.stream.function.StreamBridge;

import java.util.Map;

/**
 * 资源资源组关系导入同步消息
 *
 * <AUTHOR>
 * @date 2025/6/18 16:22
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@MessageHandler(messageType = "R_BE_ES_25", desc = "资源资源组关系导入同步消息(R_BE_ES_25)")
@Slf4j
public class SyncResourceGroupImportHandler extends AbsUnifiedAuthorizationHandler<SyncResourceGroupImportEntity> {
    protected SyncResourceGroupImportHandler(StreamBridge streamBridge) {
        super(streamBridge);
    }



    /**
     * 消息处理
     *
     * @param event                消息事件
     * @param currentHandleContext 当前handle上下文
     * @param handlesContext       当前handle组上下文
     */
    @Override
    public void handle(MessageEvent<SyncResourceGroupImportEntity> event,
                       Map<String, Object> currentHandleContext,
                       Map<String, Object> handlesContext) {
        SyncResourceGroupImportEntity payload = event.getPayload();
        Block table1 = payload.getTable1();
        Block table2 = payload.getTable2();
        Block compCode = payload.getCompCode();
        // TODO 2025-06-18 13:34:43 复合数组模型,数据拼装方式待确认
        Map<String, Object> result = Map.of(
                "table1", table1,
                "table2", table2,
                "compCode", compCode
        );

        currentHandleContext.put("forward_topic", "unified_authorization");
        currentHandleContext.put(payload.getTelegramId(), result);


    }
}
