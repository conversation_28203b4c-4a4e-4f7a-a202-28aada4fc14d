package bici.bzlj.dataprocess.decode.unifiedauthorization.user.entity;

import bici.bzlj.dataprocess.decode.unifiedauthorization.common.entity.BaseEntity;
import bici.bzlj.dataprocess.decode.unifiedauthorization.common.entity.Block;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 用户用户组关系导入同步消息
 *
 * <AUTHOR>
 * @date 2025/5/16 15:57
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SyncUserUserGroupImportEntity extends BaseEntity {
    @JsonProperty("Table1")
    private Block table1;
    @JsonProperty("Table2")
    private Block table2;
    @JsonProperty("Table3")
    private Block table3;
    @JsonProperty("COMPCODE")
    private Block compCode;
}