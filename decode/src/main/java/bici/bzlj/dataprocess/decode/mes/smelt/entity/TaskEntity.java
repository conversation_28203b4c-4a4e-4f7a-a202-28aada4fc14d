package bici.bzlj.dataprocess.decode.mes.smelt.entity;

import bici.bzlj.dataprocess.decode.annotation.ImplantAttr;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * MTDXB1 电文
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TaskEntity {

    // 单体部分字段
    /**
     * 制造命令号
     */
    @JsonProperty("PONO")
    @JsonAlias("PLAN_NO")
    private String pono;



    /**
     * 机组设备号（炉座号）
     */
    @JsonProperty("MACHINE_NO")
    private String machineNo;

    /**
     * 计划执行顺序号
     */
    @JsonProperty("PLAN_EXEC_SEQ_NO")
    @ImplantAttr("extendAttr")
    private Integer planExecSeqNo;

    /**
     * 计划个数
     */
    @JsonProperty("PLAN_NUM")
    private Integer planNum;

    /**
     * 计划重量
     */
    @JsonProperty("PLAN_WT")
    private BigDecimal planWt;

    @JsonProperty("HEAT_NO")
    private String heatNo;

    /**
     * 计划编成时刻
     */
    @JsonProperty("PLAN_MAKE_TIME")
    @ImplantAttr("extendAttr")
    private String planMakeTime;

    /**
     * 预定开始执行时刻
     */
    @JsonProperty("RESER_START_TIME")
    private String reserStartTime;

    /**
     * 预定执行结束时刻
     */
    @JsonProperty("RESER_END_TIME")
    private String reserEndTime;

    /**
     * 全程工序代码
     */
    @JsonProperty("WHOLE_BACKLOG_CODE")
    @ImplantAttr("extendAttr")
    private String wholeBacklogCode;

    /**
     * 工艺卡号（小工艺，利用其查询工艺参数）
     */
    @JsonProperty("PRACTICE_ID")
    @ImplantAttr("extendAttr")
    private String practiceId;

    /**
     * 计划备注
     */
    @JsonProperty("REMARK_PS")
    @ImplantAttr("extendAttr")
    private String remarkPs;

    /**
     * 订单属性代码
     */
    @JsonProperty("ORDER_KIND")
    @ImplantAttr("extendAttr")
    private String orderKind;

    /**
     * 后全程工序代码
     */
    @JsonProperty("NEXT_WHOLE_BACKLOG_CODE")
    @ImplantAttr("extendAttr")
    private String nextWholeBacklogCode;

    /**
     * 材料去向（17：电渣；18：自耗；10：锻造，成品锭；9A：外卖，成品锭）
     */
    @JsonProperty("MAT_DESTION")
    @ImplantAttr("extendAttr")
    private String matDestion;

    /**
     * 出钢记号
     */
    @JsonProperty("ST_NO")
    @ImplantAttr("extendAttr")
    private String stNo;

    /**
     * 品名代码
     */
    @JsonProperty("PROD_CODE")
    @ImplantAttr("extendAttr")
    private String prodCode;

    /**
     * 牌号（钢级）
     */
    @JsonProperty("SG_SIGN")
    @ImplantAttr("extendAttr")
    private String brand;

    /**
     * 标准
     */
    @JsonProperty("SG_STD")
    @ImplantAttr("extendAttr")
    private String sgStd;

    @JsonProperty("FACTORY_PROD")
    @ImplantAttr("extendAttr")
    private String factoryProd;

    @JsonProperty("ELEC_CNSM_PROFILE_ID")
    @ImplantAttr("extendAttr")
    private String elecCnsmProfileId;

    /**
     * 计量标志（0-不计量，1-计量）
     */
    @JsonProperty("WEIGHT_FLAG")
    @ImplantAttr("extendAttr")
    private String weightFlag;

    /**
     * 冷热标志（1-热送，2-冷送）
     */
    @JsonProperty("COLD_HOT_FLAG")
    @ImplantAttr("extendAttr")
    private String coldHotFlag;

    /**
     * 计划责任者
     */
    @JsonProperty("PLAN_MAKER")
    @ImplantAttr("extendAttr")
    private String planMaker;

    /**
     * 感应冶炼类型（1-真空，2-中频）
     */
    @JsonProperty("AVIM_MELT_TYPE")
    private String avimMeltType;

    /**
     * 炉代（400）
     */
    @JsonProperty("FURNANCE_ERA")
    @ImplantAttr("extendAttr")
    private Integer furnanceEra;

    /**
     * 炉龄
     */
    @JsonProperty("FURNACE_AGE")
    @ImplantAttr("extendAttr")
    private Integer furnaceAge;

    /**
     * 工场特别指令（工艺信息）
     */
    @JsonProperty("WORK_SPECIAL_REQ")
    @ImplantAttr("extendAttr")
    private String workSpecialReq;

    /**
     * 特冶冷却方式代码（工艺信息）
     */
    @JsonProperty("CLDN_PATTERN_CODE_SP")
    @ImplantAttr("extendAttr")
    private String cldnPatternCodeSp;

    /**
     * 特冶热处理工艺卡号（工艺信息）
     */
    @JsonProperty("HEAT_TREAT_RULE_CODE_SP")
    @ImplantAttr("extendAttr")
    private String heatTreatRuleCodeSp;

    /**
     * 特冶精整线代码（工艺信息）
     */
    @JsonProperty("FINISH_CODE_SP")
    @ImplantAttr("extendAttr")
    private String finishCodeSp;

    /**
     * 粗糙度要求最小值（工艺信息）
     */
    @JsonProperty("ROUGHNESS_MIN")
    @ImplantAttr("extendAttr")
    private BigDecimal roughnessMin;

    /**
     * 粗糙度要求最大值（工艺信息）
     */
    @JsonProperty("ROUGHNESS_MAX")
    @ImplantAttr("extendAttr")
    private BigDecimal roughnessMax;

    /**
     * 粗糙度目标值（工艺信息）
     */
    @JsonProperty("ROUGHNESS_AIM")
    @ImplantAttr("extendAttr")
    private BigDecimal roughnessAim;

    /**
     * 缺陷长度最大值（工艺信息）
     */
    @JsonProperty("DEFECT_LEN_MAX")
    @ImplantAttr("extendAttr")
    private BigDecimal defectLenMax;

    /**
     * 质检要求代码（工艺信息）
     */
    @JsonProperty("INSPECT_REQ_CODE")
    @ImplantAttr("extendAttr")
    private String inspectReqCode;

    /**
     * 质检要求说明（工艺信息）
     */
    @JsonProperty("INSPECT_REQ_DESC")
    @ImplantAttr("extendAttr")
    private String inspectReqDesc;

    /**
     * 外形要求代码（工艺信息）
     */
    @JsonProperty("SHP_CONTROL_CODE")
    @ImplantAttr("extendAttr")
    private String shpControlCode;

    /**
     * 自行生产/非自行生产标志（合同）（工艺信息）
     */
    @JsonProperty("SUPPLY_FLAG")
    @ImplantAttr("extendAttr")
    private String supplyFlag;

    // 备用字段
    @JsonProperty("BACK_C1")
    @ImplantAttr("extendAttr")
    private String backC1;
    @JsonProperty("BACK_C2")
    @ImplantAttr("extendAttr")
    private String backC2;
    @JsonProperty("BACK_C3")
    @ImplantAttr("extendAttr")
    private String backC3;
    @JsonProperty("BACK_C4")
    @ImplantAttr("extendAttr")
    private String backC4;
    @JsonProperty("BACK_C5")
    @ImplantAttr("extendAttr")
    private String backC5;

    // 循环部分（List集合）
    @JsonProperty("LOOP")
    @ImplantAttr("extendAttr")
    private List<Loop> loops;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Loop {
        /**
         * 入口材料号（虚拟材料号）
         */
        @JsonProperty("IN_MAT_NO")
        private String inMatNo;

        /**
         * 入口材料类型（材料类型0-虚拟；1-实物）
         */
        @JsonProperty("IN_MAT_TYPE")
        private String inMatType;

        /**
         * 合同号
         */
        @JsonProperty("ORDER_NO")
        private String orderNo;

        /**
         * 出口锭坯型
         */
        @JsonProperty("PROD_PROFILE_ID")
        private String prodProfileId;

        /**
         * 出口材料重量_1
         */
        @JsonProperty("MAT_WT_EXIT")
        private BigDecimal matWtExit;

        /**
         * 出口材料号（预定出口材料号）
         */
        @JsonProperty("OUT_MAT_NO")
        private String outMatNo;
    }
}