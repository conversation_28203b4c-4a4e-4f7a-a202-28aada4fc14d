package bici.bzlj.dataprocess.decode.mes.smelt.handler;

import bici.bzlj.dataprocess.core.annotation.MessageHandler;
import bici.bzlj.dataprocess.core.event.MessageEvent;
import bici.bzlj.dataprocess.core.utils.JsonUtils;
import bici.bzlj.dataprocess.decode.common.ForwardHandler;
import bici.bzlj.dataprocess.decode.mes.smelt.entity.TaskStatusEntity;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.stream.function.StreamBridge;

import java.util.Map;
import java.util.Objects;


/**
 * TODO
 *  任务撤销电文监听
 * <AUTHOR>
 * @date 2025/5/15 16:17
 */
@MessageHandler(messageType = "DXMTB7", desc = "任务撤销电文监听(DXMTB7)")
@Slf4j
public class TaskCancelHandler extends ForwardHandler<TaskStatusEntity> {
    private static final String SERVICE_ID = "task_change_status";

    @Override
    public String getGyckServiceId() {
        return SERVICE_ID;
    }

    protected TaskCancelHandler(StreamBridge streamBridge) {
        super(streamBridge);
    }

    /**
     * 消息处理
     *
     * @param event         消息事件
     * @param currentHandleContext 处理上下文
     */
    @Override
    public void handle(MessageEvent<TaskStatusEntity> event, Map<String, Object> currentHandleContext, Map<String, Object> handlesContext) {
        TaskStatusEntity payload =
                JsonUtils.fromJson(Objects.requireNonNull(JsonUtils.toJson(event.getPayload())), TaskStatusEntity.class);
        ObjectMapper objectMapper = new ObjectMapper();
        ObjectNode node = objectMapper.createObjectNode();
        node.put("taskCode", payload.getPono());
        node.put("taskStatus", payload.getPlanStatus());
        // 将处理结果放入上下文
        currentHandleContext.put("serviceId", SERVICE_ID);
        currentHandleContext.put(SERVICE_ID, node);
    }

}
