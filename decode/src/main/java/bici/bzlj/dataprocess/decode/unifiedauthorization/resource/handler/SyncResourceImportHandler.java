package bici.bzlj.dataprocess.decode.unifiedauthorization.resource.handler;

import bici.bzlj.dataprocess.core.annotation.MessageHandler;
import bici.bzlj.dataprocess.core.event.MessageEvent;
import bici.bzlj.dataprocess.decode.unifiedauthorization.common.entity.Block;
import bici.bzlj.dataprocess.decode.unifiedauthorization.common.handler.AbsUnifiedAuthorizationHandler;
import bici.bzlj.dataprocess.decode.unifiedauthorization.resource.entity.SyncResourceImportEntity;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.stream.function.StreamBridge;

import java.util.List;
import java.util.Map;

/**
 * 资源导入同步消息
 *
 * <AUTHOR>
 * @date 2025/6/18 16:22
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@MessageHandler(messageType = "R_BE_ES_24", desc = "资源导入同步消息(R_BE_ES_24)")
@Slf4j
public class SyncResourceImportHandler extends AbsUnifiedAuthorizationHandler<SyncResourceImportEntity> {
    protected SyncResourceImportHandler(StreamBridge streamBridge) {
        super(streamBridge);
    }



    /**
     * 消息处理
     *
     * @param event                消息事件
     * @param currentHandleContext 当前handle上下文
     * @param handlesContext       当前handle组上下文
     */
    @Override
    public void handle(MessageEvent<SyncResourceImportEntity> event,
                       Map<String, Object> currentHandleContext,
                       Map<String, Object> handlesContext) {
        SyncResourceImportEntity payload = event.getPayload();
        Block tesButtonResInfo = payload.getTesButtonResInfo();
        Block tesFormResInfo = payload.getTesFormResInfo();
        Block tesButtonResInfoRes = payload.getTesButtonResInfoRes();
        Block tesFormResInfoRes = payload.getTesFormResInfoRes();
        List<JsonNode> tesButtonResInfos = processBlockData(tesButtonResInfo);
        List<JsonNode> tesFormResInfos = processBlockData(tesFormResInfo);
        List<JsonNode> tesButtonResInfoResInfos = processBlockData(tesButtonResInfoRes);
        List<JsonNode> tesFormResInfoResInfos = processBlockData(tesFormResInfoRes);
        // TODO 2025-06-18 13:34:43 复合数组模型,数据拼装方式待确认
        Map<String, Object> result = Map.of("tesButtonResInfo", dataConvert(tesButtonResInfos),
                "tesFormResInfo", dataConvert(tesFormResInfos),
                "tesButtonResInfoRes", dataConvert(tesButtonResInfoResInfos),
                "tesFormResInfoRes", dataConvert(tesFormResInfoResInfos));

        currentHandleContext.put("forward_topic", "unified_authorization");
        currentHandleContext.put(payload.getTelegramId(), result);


    }
}
