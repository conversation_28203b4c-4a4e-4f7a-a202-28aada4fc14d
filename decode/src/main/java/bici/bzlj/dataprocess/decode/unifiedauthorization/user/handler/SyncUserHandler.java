package bici.bzlj.dataprocess.decode.unifiedauthorization.user.handler;

import bici.bzlj.dataprocess.core.annotation.MessageHandler;
import bici.bzlj.dataprocess.core.event.MessageEvent;
import bici.bzlj.dataprocess.decode.unifiedauthorization.common.entity.Block;
import bici.bzlj.dataprocess.decode.unifiedauthorization.common.handler.AbsUnifiedAuthorizationHandler;
import bici.bzlj.dataprocess.decode.unifiedauthorization.user.entity.SyncUserEntity;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.stream.function.StreamBridge;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 用户信息变更
 *
 * <AUTHOR>
 * @date 2025/5/15 16:17
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@MessageHandler(messageType = "R_BE_ES_15", desc = "用户信息变更(R_BE_ES_15)")
@Slf4j
public class SyncUserHandler extends AbsUnifiedAuthorizationHandler<SyncUserEntity> {
    protected SyncUserHandler(StreamBridge streamBridge) {
        super(streamBridge);
    }

    /**
     * 消息处理
     *
     * @param event                消息事件
     * @param currentHandleContext 处理上下文
     */
    @Override
    public void handle(MessageEvent<SyncUserEntity> event, Map<String, Object> currentHandleContext, Map<String, Object> handlesContext) {
        SyncUserEntity payload = event.getPayload();

        // 获取新增的用户
        Block insertBlock = payload.getInsertBlock();
        // 获取修改的用户
        Block updateBlock = payload.getUpdateBlock();

        // 处理新增用户数据
        List<JsonNode> insertUsers = processBlockData(insertBlock);
        // 处理更新用户数据
        List<JsonNode> updateUsers = processBlockData(updateBlock);

        // 转换数据格式
        List<Map<String, Object>> insertData = dataConvert(insertUsers);
        List<Map<String, Object>> updateData = dataConvert(updateUsers);

        // 构建结果数据 - 将两个list合并成一个数组
        Map<String, Object> result = Map.of("insertData", insertData,
                "updateData", updateData);
        // 设置转发主题和服务ID
        currentHandleContext.put("forward_topic", "unified_authorization");
        currentHandleContext.put(payload.getTelegramId(), result);
    }

    /**
     * 安全地从JsonNode中获取字符串值
     */
    private String getStringValue(JsonNode node, String fieldName) {
        JsonNode fieldNode = node.get(fieldName);
        if (fieldNode == null || fieldNode.isNull()) {
            return null;
        }
        return fieldNode.asText();
    }

    /**
     * 安全地从JsonNode中获取整数值
     */
    private Integer getIntegerValue(JsonNode node, String fieldName) {
        JsonNode fieldNode = node.get(fieldName);
        if (fieldNode == null || fieldNode.isNull()) {
            return null;
        }
        return fieldNode.asInt();
    }

    /**
     * 安全地从JsonNode中获取长整型值
     */
    private Long getLongValue(JsonNode node, String fieldName) {
        JsonNode fieldNode = node.get(fieldName);
        if (fieldNode == null || fieldNode.isNull()) {
            return null;
        }
        return fieldNode.asLong();
    }

    /**
     * 格式化时间戳为字符串
     */
    private String formatTimestamp(long timestamp) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(new Date(timestamp));
    }

}
