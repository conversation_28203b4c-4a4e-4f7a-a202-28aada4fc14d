package bici.bzlj.dataprocess.decode.mes.smelt.handler;

import bici.bzlj.dataprocess.core.annotation.MessageHandler;
import bici.bzlj.dataprocess.core.event.MessageEvent;
import bici.bzlj.dataprocess.core.utils.JsonUtils;
import bici.bzlj.dataprocess.decode.common.ForwardHandler;
import bici.bzlj.dataprocess.decode.mes.smelt.entity.CraftParamEntity;
import bici.bzlj.dataprocess.decode.mes.smelt.service.CraftParamService;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.stream.function.StreamBridge;

import java.util.Map;
import java.util.Objects;


/**
 * TODO
 *  感应_真空感应炉-CP参数电文,感应_中频感应炉-CP参数电文
 *
 * <AUTHOR>
 * @date 2025/5/15 16:17
 */
@MessageHandler(messageType = {"MTG1TB","MTG1TC"}, desc = {"感应_真空感应炉-CP参数电文(MTG1TB)","感应_中频感应炉-CP参数电文 MTG1TC"})
@Slf4j
public class CraftParamHandler extends ForwardHandler<CraftParamEntity> {

    private static final String SERVICE_ID = "craft_params";

    @Override
    public String getGyckServiceId() {
        return SERVICE_ID;
    }

    protected CraftParamHandler(StreamBridge streamBridge) {
        super(streamBridge);
    }


    /**
     * 消息处理
     *
     * @param event         消息事件
     * @param currentHandleContext 处理上下文
     */
    @Override
    public void handle(MessageEvent<CraftParamEntity> event, Map<String, Object> currentHandleContext, Map<String, Object> handlesContext) {
        CraftParamEntity payload =
                JsonUtils.fromJson(Objects.requireNonNull(JsonUtils.toJson(event.getPayload())), CraftParamEntity.class);
        JsonNode result = CraftParamService.analysisParams(payload);
        currentHandleContext.put("serviceId", SERVICE_ID);
        currentHandleContext.put(SERVICE_ID, result);
    }

}
