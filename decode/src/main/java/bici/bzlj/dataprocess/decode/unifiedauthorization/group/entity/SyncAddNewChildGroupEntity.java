package bici.bzlj.dataprocess.decode.unifiedauthorization.group.entity;

import bici.bzlj.dataprocess.decode.unifiedauthorization.common.entity.BaseEntity;
import bici.bzlj.dataprocess.decode.unifiedauthorization.common.entity.Block;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 群组新增子群组同步消息
 *
 * <AUTHOR>
 * @date 2025/6/18 09:49
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SyncAddNewChildGroupEntity extends BaseEntity {
    @JsonProperty("Table1")
    private Block parent;
    @JsonProperty("Table2")
    private Block child;
}
