package bici.bzlj.dataprocess.decode.dataConvert.cache;

import bici.bzlj.dataprocess.decode.dataConvert.entity.ProcessCodeConfig;
import bici.bzlj.dataprocess.decode.dataConvert.service.IProcessCodeConfigService;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.CacheStats;
import com.google.common.cache.LoadingCache;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * 同步配置缓存类
 * 提供基于Guava Cache的同步配置缓存功能
 * 线程安全的缓存实现
 *
 * <AUTHOR>
 * @date 2025/7/2 16:22
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@Slf4j
public class ProcessCodeConfigCache {

    private static volatile LoadingCache<String, Map<String, ProcessCodeConfig>> SYNC_CONFIG_CACHE;
    /**
     * -- GETTER --
     *  获取缓存初始化状态
     *
     */
    @Getter
    private static volatile boolean initialized = false;
    private static final Object INIT_LOCK = new Object();

    @Autowired
    public ProcessCodeConfigCache(IProcessCodeConfigService processCodeConfigService) {
        initializeCache(processCodeConfigService);
    }

    /**
     * 线程安全的缓存初始化方法
     */
    private static void initializeCache(IProcessCodeConfigService processCodeConfigService) {
        if (!initialized) {
            synchronized (INIT_LOCK) {
                if (!initialized) {
                    SYNC_CONFIG_CACHE = CacheBuilder
                            .newBuilder()
                            .maximumSize(1000)  // 增加缓存大小
                            .expireAfterWrite(30, TimeUnit.MINUTES)  // 写入后30分钟过期
                            .expireAfterAccess(10, TimeUnit.MINUTES)  // 访问后10分钟过期
                            .recordStats()  // 启用统计
                            .removalListener(notification -> {
                                log.info("缓存移除 - 原因: {}, Key: {}, Value: {}",
                                        notification.getCause().name(),
                                        notification.getKey(),
                                        notification.getValue());
                            })
                            .build(new CacheLoader<String, Map<String, ProcessCodeConfig>>() {
                                @Override
                                public Map<String, ProcessCodeConfig> load(String key) throws Exception {
                                    log.debug("从数据库加载同步配置, serviceId: {}", key);
                                    return processCodeConfigService.getProcessCodeConfigMapByServiceId(key);
                                }
                            });
                    initialized = true;
                    log.info("同步配置缓存初始化完成");
                }
            }
        }
    }

    /**
     * 检查缓存是否未初始化
     */
    private static boolean isCacheNotInitialized() {
        return !initialized || SYNC_CONFIG_CACHE == null;
    }

    /**
     * 根据服务ID获取同步配置映射
     *
     * @param key 服务ID
     * @return 同步配置映射，如果不存在则返回空Map
     */
    public static Map<String, ProcessCodeConfig> get(String key) {
        if (isCacheNotInitialized()) {
            log.warn("缓存未初始化，返回空Map, serviceId: {}", key);
            return Map.of();
        }
        try {
            return SYNC_CONFIG_CACHE.get(key);
        } catch (Exception e) {
            log.error("获取缓存失败, serviceId: {}", key, e);
            return Map.of();
        }
    }

    /**
     * 获取同步配置映射，如果不存在则返回默认值
     *
     * @param serviceId    服务ID
     * @param defaultValue 默认值
     * @return 同步配置映射
     */
    public static Map<String, ProcessCodeConfig> getOrDefault(String serviceId, Map<String, ProcessCodeConfig> defaultValue) {
        if (isCacheNotInitialized()) {
            log.warn("缓存未初始化，返回默认值, serviceId: {}", serviceId);
            return defaultValue;
        }
        try {
            Map<String, ProcessCodeConfig> result = SYNC_CONFIG_CACHE.get(serviceId);
            return result.isEmpty() ? defaultValue : result;
        } catch (ExecutionException e) {
            log.error("获取缓存失败, serviceId: {}", serviceId, e);
            return defaultValue;
        }
    }

    /**
     * 手动放入缓存
     *
     * @param key 服务ID
     * @param configMap 配置映射
     */
    public static void put(String key, Map<String, ProcessCodeConfig> configMap) {
        if (isCacheNotInitialized()) {
            log.warn("缓存未初始化，无法放入缓存, serviceId: {}", key);
            return;
        }
        SYNC_CONFIG_CACHE.put(key, configMap);
        log.debug("手动放入缓存, serviceId: {}, configMap: {}", key, configMap);
    }

    /**
     * 移除指定服务ID的缓存
     *
     * @param serviceId 服务ID
     */
    public static void remove(String serviceId) {
        if (isCacheNotInitialized()) {
            log.warn("缓存未初始化，无法移除缓存, serviceId: {}", serviceId);
            return;
        }
        SYNC_CONFIG_CACHE.invalidate(serviceId);
        log.debug("移除缓存, serviceId: {}", serviceId);
    }

    /**
     * 清空所有缓存
     */
    public static void clear() {
        if (isCacheNotInitialized()) {
            log.warn("缓存未初始化，无法清空缓存");
            return;
        }
        SYNC_CONFIG_CACHE.invalidateAll();
        log.info("清空所有缓存");
    }

    /**
     * 刷新指定服务ID的缓存
     *
     * @param serviceId 服务ID
     */
    public static void refresh(String serviceId) {
        if (isCacheNotInitialized()) {
            log.warn("缓存未初始化，无法刷新缓存, serviceId: {}", serviceId);
            return;
        }
        try {
            SYNC_CONFIG_CACHE.refresh(serviceId);
            log.debug("刷新缓存, serviceId: {}", serviceId);
        } catch (Exception e) {
            log.error("刷新缓存失败, serviceId: {}", serviceId, e);
        }
    }

    /**
     * 检查缓存中是否存在指定的服务ID
     *
     * @param serviceId 服务ID
     * @return 是否存在
     */
    public static boolean containsKey(String serviceId) {
        if (isCacheNotInitialized()) {
            log.warn("缓存未初始化，返回false, serviceId: {}", serviceId);
            return false;
        }
        return SYNC_CONFIG_CACHE.getIfPresent(serviceId) != null;
    }

    /**
     * 获取缓存大小
     *
     * @return 缓存大小
     */
    public static long size() {
        if (isCacheNotInitialized()) {
            log.warn("缓存未初始化，返回0");
            return 0;
        }
        return SYNC_CONFIG_CACHE.size();
    }

    /**
     * 获取缓存统计信息
     *
     * @return 缓存统计信息
     */
    public static CacheStats getStats() {
        if (isCacheNotInitialized()) {
            log.warn("缓存未初始化，返回null");
            return null;
        }
        return SYNC_CONFIG_CACHE.stats();
    }

    /**
     * 打印缓存统计信息
     */
    public static void printStats() {
        if (isCacheNotInitialized()) {
            log.warn("缓存未初始化，无法打印统计信息");
            return;
        }
        CacheStats stats = SYNC_CONFIG_CACHE.stats();
        double hitRate = stats.hitRate() * 100;
        double avgLoadTime = stats.averageLoadPenalty() / 1_000_000.0;

        log.info("缓存统计信息: 命中率={}%, 命中次数={}, 未命中次数={}, 加载次数={}, 平均加载时间={}ms, 缓存大小={}",
                String.format("%.2f", hitRate),
                stats.hitCount(),
                stats.missCount(),
                stats.loadCount(),
                String.format("%.2f", avgLoadTime),
                SYNC_CONFIG_CACHE.size());
    }



    /**
     * 批量获取多个服务的配置
     * 线程安全的批量操作
     *
     * @param keys 服务ID列表
     * @return 服务ID到配置映射的Map
     */
    public static Map<String, Map<String, ProcessCodeConfig>> batchGet(Iterable<String> keys) {
        if (isCacheNotInitialized()) {
            log.warn("缓存未初始化，返回空Map");
            return Map.of();
        }
        try {
            return SYNC_CONFIG_CACHE.getAll(keys);
        } catch (ExecutionException e) {
            log.error("批量获取缓存失败", e);
            return Map.of();
        }
    }

}
