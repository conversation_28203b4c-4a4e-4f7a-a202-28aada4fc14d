package bici.bzlj.dataprocess.decode.mes.smelt.convert;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.Map;

@Data
public class InspectionData {
    // 核心字段（17个）
    @JsonProperty("全程工序代码")
    private String processCode;
    
    @JsonProperty("全程工序顺序号")
    private String processSequence;
    
    @JsonProperty("试料组（批）号")
    private String sampleGroupBatchNo;
    
    @JsonProperty("试验过程标识")
    private String testProcessFlag;
    
    @JsonProperty("试样记录编号")
    private String sampleRecordNo;
    
    @JsonProperty("试料部位")
    private String sampleLocation;
    
    @JsonProperty("试验条件")
    private String testCondition;
    
    @JsonProperty("试验项目代码")
    private String testItemCode;
    
    @JsonProperty("取样目的代码")
    private String samplingPurposeCode;

    @JsonProperty("试样号")
    private String sampleNum;
    
    @JsonProperty("合同号")
    private String contractNo;
    
    @JsonProperty("PONO号")
    private String taskCode;
    
    @JsonProperty("代表材料号")
    private String representativeMaterialNo;
    
    @JsonProperty("热处理号")
    private String heatTreatmentNo;
    
    @JsonProperty("材料号_2")
    private String materialNo2;
    
    @JsonProperty("标准")
    private String standard;
    
    @JsonProperty("钢种牌号")
    private String steelGrade;
    
    @JsonProperty("试验时间")
    private String testTime;
    
    // 特殊字段
    @JsonProperty("炉号")
    private String furnaceNo;
    
    @JsonProperty("母炉号")
    private String motherFurnaceNo;
    
    @JsonProperty("理化性能判定码")
    private String judgmentCode;
    
    @JsonProperty("数据来源")
    private String dataSource;

    private String ruleCode;

    private String ruleName;
    
    // 额外字段存储
    private Map<String, Object> extraFields;
}