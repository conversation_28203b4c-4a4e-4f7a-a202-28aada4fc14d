package bici.bzlj.dataprocess.decode.unifiedauthorization.resource.entity;

import bici.bzlj.dataprocess.decode.unifiedauthorization.common.entity.BaseEntity;
import bici.bzlj.dataprocess.decode.unifiedauthorization.common.entity.Block;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 资源群组删除资源同步消息
 *
 * <AUTHOR>
 * @date 2025/6/18 15:10
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SyncDeleteResourceGroupEntity extends BaseEntity {
    @JsonProperty("Table1")
    private Block table1;
}
