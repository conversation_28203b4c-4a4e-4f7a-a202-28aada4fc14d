package bici.bzlj.dataprocess.decode.unifiedauthorization.resource.entity;

import bici.bzlj.dataprocess.decode.unifiedauthorization.common.entity.BaseEntity;
import bici.bzlj.dataprocess.decode.unifiedauthorization.common.entity.Block;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 资源组人员组关系维护同步消息
 *
 * <AUTHOR>
 * @date 2025/6/18 15:53
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SyncResourceUserGroupEntity extends BaseEntity {
    @JsonProperty("Table1")
    private Block table1;
    @JsonProperty("GROUP_RES_ADD")
    private Block groupResAdd;
    @JsonProperty("GROUP_RES_RMV")
    private Block groupResRmv;
    @JsonProperty("RES_GROUP_ADD")
    private Block resGroupAdd;
    @JsonProperty("RES_GROUP_RMV")
    private Block resGroupRmv;
}
