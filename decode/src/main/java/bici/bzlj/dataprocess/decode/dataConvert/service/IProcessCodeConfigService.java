package bici.bzlj.dataprocess.decode.dataConvert.service;

import bici.bzlj.dataprocess.decode.dataConvert.entity.DataMappingConfig;
import bici.bzlj.dataprocess.decode.dataConvert.entity.ProcessCodeConfig;
import bici.bzlj.dataprocess.decode.dataConvert.policy.ExcelImportPolicy;

import java.util.List;
import java.util.Map;

public interface IProcessCodeConfigService extends ExcelImportPolicy {

    String importSyncConfig(List<ProcessCodeConfig> processCodeConfigs);

    Map<String, ProcessCodeConfig> getProcessCodeConfigMapByServiceId(String serviceId);

}

