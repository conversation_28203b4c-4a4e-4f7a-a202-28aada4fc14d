package bici.bzlj.dataprocess.decode.unifiedauthorization.view.handler;

import bici.bzlj.dataprocess.core.annotation.MessageHandler;
import bici.bzlj.dataprocess.core.event.MessageEvent;
import bici.bzlj.dataprocess.decode.unifiedauthorization.common.entity.Block;
import bici.bzlj.dataprocess.decode.unifiedauthorization.common.handler.AbsUnifiedAuthorizationHandler;
import bici.bzlj.dataprocess.decode.unifiedauthorization.view.entity.SyncViewEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.stream.function.StreamBridge;

import java.util.Map;

/**
 * 画面维护同步消息
 *
 * <AUTHOR>
 * @date 2025/6/18 09:57
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@MessageHandler(messageType = "R_BE_ES_19", desc = "画面维护同步消息(R_BE_ES_19)")
@Slf4j
public class SyncViewHandler extends AbsUnifiedAuthorizationHandler<SyncViewEntity> {

    protected SyncViewHandler(StreamBridge streamBridge) {
        super(streamBridge);
    }



    /**
     * 消息处理
     *
     * @param event                消息事件
     * @param currentHandleContext 当前handle上下文
     * @param handlesContext       当前handle组上下文
     */
    @Override
    public void handle(MessageEvent<SyncViewEntity> event,
                       Map<String, Object> currentHandleContext,
                       Map<String, Object> handlesContext) {
        SyncViewEntity payload = event.getPayload();
        Block insertBlock = payload.getInsertBlock();
        Block updateBlock = payload.getUpdateBlock();
        Block deleteBlock = payload.getDeleteBlock();
        Map<String, Object> result = Map.of("insertGroups", dataConvert(processBlockData(insertBlock)),
                "updateGroups", dataConvert(processBlockData(updateBlock)),
                "deleteGroups", dataConvert(processBlockData(deleteBlock)));
        currentHandleContext.put("forward_topic", "unified_authorization");
        currentHandleContext.put(payload.getTelegramId(), result);
    }
}
