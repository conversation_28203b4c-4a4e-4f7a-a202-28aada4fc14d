package bici.bzlj.dataprocess.decode.unifiedauthorization.group.entity;

import bici.bzlj.dataprocess.decode.unifiedauthorization.common.entity.BaseEntity;
import bici.bzlj.dataprocess.decode.unifiedauthorization.common.entity.Block;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 群组删除用户同步消息
 *
 * <AUTHOR>
 * @date 2025/6/18 09:49
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SyncDeleteGroupUsersEntity extends BaseEntity {
    @JsonProperty("Table3")
    private Block userInfo;
    @JsonProperty("Table1")
    private Block groupInfo;
    @JsonProperty("Table2")
    private Block table2;
}
