package bici.bzlj.dataprocess.decode.dataConvert.repository;

import bici.bzlj.dataprocess.decode.dataConvert.entity.DataMappingConfig;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;

public interface DataMappingConfigRepository extends MongoRepository<DataMappingConfig, String> {

    List<DataMappingConfig> findByServiceId(String serviceId);
    List<DataMappingConfig> findByServiceIdAndType(String serviceId,String type);

    void deleteAllByServiceIdIn(List<String> serviceIds);
}