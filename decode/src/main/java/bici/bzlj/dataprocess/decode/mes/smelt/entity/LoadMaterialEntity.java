package bici.bzlj.dataprocess.decode.mes.smelt.entity;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * DXMTM2,DYMTN3
 * 原料消耗实绩电文
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LoadMaterialEntity {

    /**
     * 制造命令号/计划号
     */
    @JsonProperty("PLAN_NO")
    @JsonAlias("PONO")
    private String planNo;

    /**
     * 熔炼号
     */
    @JsonProperty("HEAT_NO")
    private String heatNo;

    /**
     * 材料代码
     */
    @JsonProperty("MATERIAL_CODE")
    private String materialCode;

    /**
     * 批次号_YF
     */
    @JsonProperty("RAW_BATCH_NO")
    private String rawBatchNo;

    /**
     * 物料名称_001
     */
    @JsonProperty("MATERIAL_NAME")
    private String materialName;

    /**
     * 材料重量
     */
    @JsonProperty("MAT_WT")
    private BigDecimal matWt;

    /**
     * 库位号_YF
     */
    @JsonProperty("YARD_PLACE_NO")
    private String yardPlaceNo;
}
