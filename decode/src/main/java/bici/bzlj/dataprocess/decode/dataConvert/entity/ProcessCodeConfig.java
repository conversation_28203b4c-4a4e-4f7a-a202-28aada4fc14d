package bici.bzlj.dataprocess.decode.dataConvert.entity;

import bici.bzlj.dataprocess.decode.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2025/7/2 14:27
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@Accessors(chain = true)
@Document(collection = "process_code_config")
@Data
public class ProcessCodeConfig implements Serializable {

    @Excel(name = "ID")
    @Id
    private String id;

    @Excel(name = "原始工序代码")
    private String originalProcessCode;

    @Excel(name = "工序代码")
    private String processCode;

    /**
     * 来源字段
     */
    @Excel(name = "服务ID")
    private String serviceId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @CreatedDate
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @LastModifiedDate
    private Date updateTime;

}
