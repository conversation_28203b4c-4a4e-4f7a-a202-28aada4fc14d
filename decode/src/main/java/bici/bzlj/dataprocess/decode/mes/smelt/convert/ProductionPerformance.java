package bici.bzlj.dataprocess.decode.mes.smelt.convert;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class ProductionPerformance {
    /**
     * 制造命令号 必须
     */
    private String taskCode;

    /**
     * 实际开始时间
     */
    private String startTime;

    /**
     * 实际结束时间
     */
    private String endTime;

    /**
     * 投入物料
     */
    private List<String> inputMaterials;

    /**
     * 生产实绩--（雷震说当操作记录）
     */
    private Map<String, Object> operationLog;

    /**
     * 产出物料
     */
    private List<OutputMaterial> outputMaterials;

    /**
     * 班组信息
     */
    private TeamInfo teamInfo;

    /**
     * 是否投入等于产出
     */
    private Boolean inputEQOutput = false;

    @Data
    public static class OutputMaterial {
        /**
         * 物料编码
         */
        private String materialCode;

        private String heatNumber;

        /**
         * 物理属性
         */
        private Map<String, Object> physicalAttr;

        /**
         * 规格属性
         */
        private Map<String, Object> specificationAttr;
    }


    @Data
    public static class TeamInfo {
        //开始班别
        private String startShiftGroup;

        //开始班次
        private String startShiftNo;

        //开始班组组长
        private String startShiftLeader;

        private String endShiftGroup;

        private String endShiftNo;

        private String endShiftLeader;
    }


}
