package bici.bzlj.dataprocess.decode.mes.pipe.handler;

import bici.bzlj.dataprocess.core.annotation.MessageHandler;
import bici.bzlj.dataprocess.core.event.MessageEvent;
import bici.bzlj.dataprocess.decode.common.ForwardHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.stream.function.StreamBridge;

import java.util.List;
import java.util.Map;


/**
 * TODO
 *  特冶感应炉材料产出实绩电文接收(DXMTM1)
 *
 * <AUTHOR>
 * @date 2025/5/15 16:17
 */
@MessageHandler(messageType = {"HEMT01"}, desc = "产出实绩电文接收")
@Slf4j
public class PipeProductionPerformanceHandler extends ForwardHandler<String> {

    private static final String SERVICE_ID = "production_performance";

    //投入等于产出的电文
    private static final List<String> inputEqualProduction = List.of();

    protected PipeProductionPerformanceHandler(StreamBridge streamBridge) {
        super(streamBridge);
    }

    @Override
    public String getGyckServiceId() {
        return SERVICE_ID;
    }

    /**
     * 消息处理
     *
     * @param event         消息事件
     * @param currentHandleContext 处理上下文
     */
    @Override
    public void handle(MessageEvent<String> event, Map<String, Object> currentHandleContext, Map<String, Object> handlesContext) {
        // 将处理结果放入上下文
        currentHandleContext.put("serviceId", SERVICE_ID);
        currentHandleContext.put(SERVICE_ID, event.getConvertData());

    }

}
