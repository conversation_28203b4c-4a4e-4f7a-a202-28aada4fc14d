package bici.bzlj.dataprocess.decode.unifiedauthorization.group.handler;

import bici.bzlj.dataprocess.core.annotation.MessageHandler;
import bici.bzlj.dataprocess.core.event.MessageEvent;
import bici.bzlj.dataprocess.decode.unifiedauthorization.common.entity.Block;
import bici.bzlj.dataprocess.decode.unifiedauthorization.common.handler.AbsUnifiedAuthorizationHandler;
import bici.bzlj.dataprocess.decode.unifiedauthorization.group.entity.SyncDeleteGroupUsersEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.stream.function.StreamBridge;

import java.util.List;
import java.util.Map;

/**
 * 群组删除用户同步消息
 *
 * <AUTHOR>
 * @date 2025/6/18 13:07
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@MessageHandler(messageType = "R_BE_ES_18", desc = "群组删除用户同步消息(R_BE_ES_18)")
@Slf4j
public class SyncDeleteGroupUsersHandler extends AbsUnifiedAuthorizationHandler<SyncDeleteGroupUsersEntity> {
    protected SyncDeleteGroupUsersHandler(StreamBridge streamBridge) {
        super(streamBridge);
    }


    /**
     * 消息处理
     *
     * @param event                消息事件
     * @param currentHandleContext 当前handle上下文
     * @param handlesContext       当前handle组上下文
     */
    @Override
    public void handle(MessageEvent<SyncDeleteGroupUsersEntity> event,
                       Map<String, Object> currentHandleContext,
                       Map<String, Object> handlesContext) {
        SyncDeleteGroupUsersEntity payload = event.getPayload();
        Block groupInfo = payload.getGroupInfo();
        Block userInfo = payload.getUserInfo();
        List<Map<String, Object>> deleteData = getDealParentChildData(groupInfo, userInfo);
        Map<String, Object> result = Map.of("deleteData", deleteData);
        currentHandleContext.put("forward_topic", "unified_authorization");
        currentHandleContext.put(payload.getTelegramId(), result);
    }
}
