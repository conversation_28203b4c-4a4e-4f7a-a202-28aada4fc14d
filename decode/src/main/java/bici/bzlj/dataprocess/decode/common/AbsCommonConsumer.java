package bici.bzlj.dataprocess.decode.common;

import bici.bzlj.dataprocess.core.event.MessageEvent;
import bici.bzlj.dataprocess.core.producer.MessageEventProducer;
import bici.bzlj.dataprocess.core.utils.JsonUtils;
import bici.bzlj.dataprocess.core.utils.SnowflakeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2025/5/29 17:31
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@Slf4j
public abstract class AbsCommonConsumer {
    @Autowired
    private MessageEventProducer messageEventProducer;


    /**
     * 公共的数据处理逻辑
     *
     * @param messageInfo
     * @param modelName
     * <AUTHOR>
     * @date 2025/5/29 17:44
     */
    public void handleData(CommonMessageInfo<?> messageInfo, String modelName) {
        try {
            MessageEvent event = new MessageEvent();
            event.setMessageId(messageInfo.getType() + "-" + SnowflakeUtils.nextStrId());
            event.setMessageType(messageInfo.getId());
            event.setPayload(messageInfo.getPayload());
            event.setConvertData(messageInfo.getConvertData());
            messageEventProducer.pushMessage(event);
        } catch (Exception e) {
            log.error("消费[{}]kafka数据失败:电文分类:{},电文号:{},电文内容:{}", modelName,
                    messageInfo.getType(),
                    messageInfo.getId(),
                    JsonUtils.toJson(messageInfo),
                    e);
        }
    }



}
