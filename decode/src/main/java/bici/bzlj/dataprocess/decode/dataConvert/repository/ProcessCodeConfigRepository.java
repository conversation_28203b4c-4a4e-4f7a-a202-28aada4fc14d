package bici.bzlj.dataprocess.decode.dataConvert.repository;

import bici.bzlj.dataprocess.decode.dataConvert.entity.DataMappingConfig;
import bici.bzlj.dataprocess.decode.dataConvert.entity.ProcessCodeConfig;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;

public interface ProcessCodeConfigRepository extends MongoRepository<ProcessCodeConfig, String> {

    List<ProcessCodeConfig> findByServiceId(String serviceId);

    void deleteAllByServiceIdIn(List<String> serviceIds);
}