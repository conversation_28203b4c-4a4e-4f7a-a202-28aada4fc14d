package bici.bzlj.dataprocess.decode.mes.smelt.entity;

import bici.bzlj.dataprocess.decode.annotation.ImplantAttr;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * MTG1TB、MTG1TC
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CraftParamEntity {

    /**
     * 入口材料号
     */
    @JsonProperty("MAT_NO")
    private String matNo;

    /**
     * 合同号
     */
    @JsonProperty("ORDER_NO")
    private String orderNo;

    /**
     * 计划号
     */
    @JsonProperty("PLAN_NO")
    private String planNo;

    /**
     * 最后包标志
     * 1:计划里最后一笔材料 ??
     */
    @JsonProperty("END_FLAG")
    private String endFlag;

    @JsonProperty("LOOP")
    @ImplantAttr
    private List<Loop> loops;

    @Data
    public static class Loop {
        /**
         * CP参数名
         * 若某个特性的目标、上/下限 同时为-9999（无要求），则该特性一组信息都不拼入电文。
         * 所有有要求的特性，按CP参数名的顺序连续拼接电文数据
         */
        @JsonProperty("CP_ITEM_NAME")
        private String cpItemName;

        /**
         * CP级别
         * 1：参与控制
         * 2：展示
         * 3：参与控制+展示
         */
        @JsonProperty("CP_ITEM_LEVEL")
        private String cpItemLevel;

        /**
         * CP目标值
         * -9999:无要求
         * 代码要求/文本描述的特性存储此列
         */
        @JsonProperty("CP_ITEM_AIM")
        private String cpItemAim;

        /**
         * CP上限
         * -9999:无要求
         */
        @JsonProperty("CP_ITEM_MAX")
        private String cpItemMax;

        /**
         * CP下限
         * -9999:无要求
         */
        @JsonProperty("CP_ITEM_MIN")
        private String cpItemMin;

        /**
         * 反应计划
         * CP的反应计划文本，超长截取部分
         */
        @JsonProperty("RESPONSE_PLAN_DESC")
        private String responsePlanDesc;

        /**
         * 关注区域(原料接收)
         * 原料接收段的关注特性
         */
        @JsonProperty("PRC_CHARACT_REC")
        private String prcCharactRec;

        /**
         * 关注区域(原料加工)
         * 原料加工段的关注特性
         */
        @JsonProperty("PRC_CHARACT_PRO")
        private String prcCharactPro;

        /**
         * 关注区域(辅料工装准备)
         * 辅料工装准备段的关注特性
         */
        @JsonProperty("PRC_CHARACT_AUX")
        private String prcCharactAux;

        /**
         * 关注区域(配料)
         * 配料段的关注特性
         */
        @JsonProperty("PRC_CHARACT_ING")
        private String prcCharactIng;

        /**
         * 关注区域(装料)
         * 装料段的关注特性
         */
        @JsonProperty("PRC_CHARACT_CHA")
        private String prcCharactCha;

        /**
         * 关注区域(感应熔炼)
         * 感应熔炼段的关注特性
         */
        @JsonProperty("PRC_CHARACT_SME")
        private String prcCharactSme;

        /**
         * 关注区域(浇铸)
         * 浇铸段的关注特性
         */
        @JsonProperty("PRC_CHARACT_CAST")
        private String prcCharactCast;
    }

}
