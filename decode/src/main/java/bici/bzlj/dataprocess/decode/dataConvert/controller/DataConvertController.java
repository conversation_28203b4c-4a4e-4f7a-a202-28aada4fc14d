package bici.bzlj.dataprocess.decode.dataConvert.controller;

import bici.bzlj.dataprocess.decode.dataConvert.entity.DataMappingConfig;
import bici.bzlj.dataprocess.decode.dataConvert.factory.ExcelImportFactory;
import bici.bzlj.dataprocess.decode.dataConvert.policy.ExcelImportPolicy;
import bici.bzlj.dataprocess.decode.dataConvert.service.IDataMappingConfigService;
import bici.bzlj.dataprocess.decode.util.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@RestController
@RequestMapping("/sync/config")
@Slf4j
@RequiredArgsConstructor
public class DataConvertController {

    private final IDataMappingConfigService dataMappingConfigService;

    /**
     * 导入同步配置Excel文件
     */
    @PostMapping("/importSyncConfigByExcel")
    public void importSyncConfigByExcel(@RequestParam(value = "file") MultipartFile file,  @RequestParam("type") String type) {
        try {
            ExcelImportPolicy policy = ExcelImportFactory.getPolicy(type);
            String message = policy.importExcel(file.getInputStream());
            log.info(message);
        } catch (Exception e) {
            log.error("导入Excel时发生错误", e);
        }
    }

}
