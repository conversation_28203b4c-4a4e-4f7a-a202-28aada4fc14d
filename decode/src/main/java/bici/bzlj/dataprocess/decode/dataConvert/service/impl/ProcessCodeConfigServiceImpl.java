package bici.bzlj.dataprocess.decode.dataConvert.service.impl;

import bici.bzlj.dataprocess.decode.dataConvert.cache.ProcessCodeConfigCache;
import bici.bzlj.dataprocess.decode.dataConvert.constant.Constants;
import bici.bzlj.dataprocess.decode.dataConvert.entity.ProcessCodeConfig;
import bici.bzlj.dataprocess.decode.dataConvert.repository.ProcessCodeConfigRepository;
import bici.bzlj.dataprocess.decode.dataConvert.service.IProcessCodeConfigService;
import bici.bzlj.dataprocess.decode.util.StringUtils;
import bici.bzlj.dataprocess.decode.util.poi.ExcelUtil;
import com.beust.jcommander.internal.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.InputStream;
import java.util.List;
import java.util.Map;

@Service(value = Constants.EXCEL_IMPORT_POLICY + "processCode")
@Slf4j
@RequiredArgsConstructor
public class ProcessCodeConfigServiceImpl implements IProcessCodeConfigService {

    private final ProcessCodeConfigRepository processCodeConfigRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importSyncConfig(List<ProcessCodeConfig> processCodeConfigs) {
        // 校验导入的配置
        checkConfig(processCodeConfigs);

        // 获取所有受影响的serviceId，用于后续缓存刷新
        List<String> affectedServiceIds = processCodeConfigs.stream()
                .map(ProcessCodeConfig::getServiceId)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .toList();

        // 获取id集合
        List<String> ids = processCodeConfigs.stream().map(ProcessCodeConfig::getId).toList();
        // 删除原有配置
        processCodeConfigRepository.deleteAllById(ids);
        // 插入新配置
        for (ProcessCodeConfig processCodeConfig : processCodeConfigs) {
            processCodeConfigRepository.insert(processCodeConfig);
        }

        // 刷新受影响的缓存
        int refreshedCount = refreshCacheForServiceIds(affectedServiceIds);

        log.info("成功导入{}条同步配置，涉及{}个服务，实际刷新缓存{}个",
                processCodeConfigs.size(), affectedServiceIds.size(), refreshedCount);

        return String.format("成功导入%d条配置，刷新了%d个服务的缓存",
                processCodeConfigs.size(), refreshedCount);
    }

    @Override
    public Map<String, ProcessCodeConfig> getProcessCodeConfigMapByServiceId(String serviceId) {
        List<ProcessCodeConfig> configs = processCodeConfigRepository.findByServiceId(serviceId);
        if (CollectionUtils.isEmpty(configs)) {
            return Map.of();
        }
        Map<String, ProcessCodeConfig> result = Maps.newHashMap();
        configs.forEach(config -> {
            result.put(config.getOriginalProcessCode(), config);
        });
        return result;
    }


    /**
     * 校验导入的配置
     *
     * @param processCodeConfigs 同步配置列表
     */
    private static void checkConfig(List<ProcessCodeConfig> processCodeConfigs) {
        if (CollectionUtils.isEmpty(processCodeConfigs)) {
            throw new RuntimeException("导入配置为空！");
        }
        long countServiceIds = processCodeConfigs
                .stream()
                .map(ProcessCodeConfig::getServiceId)
                .filter(StringUtils::isNotBlank)
                .count();
        // 判断serviceId是否存在未填写的
        if (countServiceIds != processCodeConfigs.size()) {
            throw new RuntimeException("导入配置存在未填写的serviceId！");
        }
        // 判断是否存在未填写的原始工序代码
        long countOriginal = processCodeConfigs
                .stream()
                .map(ProcessCodeConfig::getOriginalProcessCode)
                .filter(StringUtils::isNotBlank)
                .count();
        if (countOriginal != processCodeConfigs.size()) {
            throw new RuntimeException("导入配置存在未填写的原始工序代码！");
        }

        long countProcessCode = processCodeConfigs
                .stream()
                .map(ProcessCodeConfig::getProcessCode)
                .filter(StringUtils::isNotBlank)
                .count();
        if (countProcessCode != processCodeConfigs.size()) {
            throw new RuntimeException("导入配置存在未填写的工序代码！");
        }
        //判断是否存在重复的serviceId和原始工序代码、工序代码 组合
        long count = processCodeConfigs
                .stream()
                .map(processCodeConfig -> StringUtils.join(processCodeConfig.getServiceId(),
                        "_",
                        processCodeConfig.getOriginalProcessCode(),
                        "_",
                        processCodeConfig.getProcessCode()))
                .distinct().count();
        if (count != processCodeConfigs.size()) {
            throw new RuntimeException("导入配置存在重复的serviceId,原始工序代码和工序代码组合！");
        }
        // 初始化id
        processCodeConfigs.parallelStream().forEach(processCodeConfig -> {
            if (StringUtils.isBlank(processCodeConfig.getId())) {
                processCodeConfig.setId(StringUtils.join(processCodeConfig.getServiceId(),
                        "_",
                        processCodeConfig.getOriginalProcessCode(),
                        "_",
                        processCodeConfig.getProcessCode()));
            }
        });
    }

    /**
     * 刷新指定服务ID列表的缓存
     *
     * @param keys 服务ID列表
     * @return 实际刷新成功的数量
     */
    private int refreshCacheForServiceIds(List<String> keys) {
        if (CollectionUtils.isEmpty(keys)) {
            log.debug("没有需要刷新的缓存");
            return 0;
        }

        log.info("开始刷新{}个服务的缓存: {}", keys.size(), keys);

        int successCount = 0;
        int failCount = 0;

        for (String key : keys) {
            try {
                // 刷新缓存
                ProcessCodeConfigCache.refresh(key);
                successCount++;
                log.debug("成功刷新缓存, serviceId: {}", key);
            } catch (Exception e) {
                failCount++;
                log.error("刷新缓存失败, serviceId: {}", key, e);
                // 如果刷新失败，尝试移除缓存，让下次访问时重新加载
                try {
                    ProcessCodeConfigCache.remove(key);
                    log.debug("移除缓存成功, serviceId: {}", key);
                } catch (Exception removeEx) {
                    log.error("移除缓存也失败, serviceId: {}", key, removeEx);
                }
            }
        }

        log.info("缓存刷新完成: 成功{}个, 失败{}个", successCount, failCount);
        return successCount;
    }




    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importExcel(InputStream inputStream) {
        ExcelUtil<ProcessCodeConfig> util = new ExcelUtil<>(ProcessCodeConfig.class);
        List<ProcessCodeConfig> processCodeConfigs = util.importExcel(inputStream);
        return importSyncConfig(processCodeConfigs);
    }
}

