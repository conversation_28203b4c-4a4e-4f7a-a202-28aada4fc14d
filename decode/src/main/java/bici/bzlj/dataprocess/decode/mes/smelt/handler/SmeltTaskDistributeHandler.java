package bici.bzlj.dataprocess.decode.mes.smelt.handler;

import bici.bzlj.dataprocess.core.annotation.MessageHandler;
import bici.bzlj.dataprocess.core.event.MessageEvent;
import bici.bzlj.dataprocess.core.utils.JsonUtils;
import bici.bzlj.dataprocess.decode.common.ForwardHandler;
import bici.bzlj.dataprocess.decode.util.StringUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.stream.function.StreamBridge;

import java.util.Map;
import java.util.Objects;


/**
 * TODO
 *  任务下发电文监听
 *
 * <AUTHOR>
 * @date 2025/5/15 16:17
 */
@MessageHandler(messageType = {"MTDYC1","MTDZD1","MTDYE1","MTDXB1","MTDZE1","MTDXE1"}, desc = "任务下发电文监听")
@Slf4j
public class SmeltTaskDistributeHandler extends ForwardHandler<String> {

    protected SmeltTaskDistributeHandler(StreamBridge streamBridge) {
        super(streamBridge);
    }

    @Override
    public String getGyckServiceId() {
        return SERVICE_ID;
    }

    private static final String SERVICE_ID = "task_Distribute";

    /**
     * 消息处理
     *
     * @param event                消息事件
     * @param currentHandleContext 处理上下文
     */
    @Override
    public void handle(MessageEvent<String> event, Map<String, Object> currentHandleContext, Map<String, Object> handlesContext) {
        if (event.getMessageType().equals("MTDXB1")) {
            String convertData = event.getConvertData();
            JsonNode jsonNode = JsonUtils.toJsonNode(convertData);
            if (jsonNode instanceof ArrayNode) {
                jsonNode.forEach(this::dealJsoNode);
            }else {
                dealJsoNode(jsonNode);
            }
            event.setConvertData(JsonUtils.toJson(jsonNode));
        }
        // 将处理结果放入上下文
        currentHandleContext.put("serviceId", SERVICE_ID);
        currentHandleContext.put(SERVICE_ID, event.getConvertData());
    }

    /**
     * 处理数据
     *
     * @param node
     */
    private void dealJsoNode(JsonNode node){
        JsonNode processCodeNode = node.get("processCode");
        if (Objects.nonNull(processCodeNode)) {
            String processCode = "TB";
            try {
                processCode = StringUtils.equals(processCodeNode.asText().trim(), "1") ? "TB" : "TC";
            } catch (Exception e) {
                log.error("AVIM_MELT_TYPE转化错误: {}", processCodeNode.asText());
            }
            // 将processCode添加到当前节点
            ((ObjectNode) node).put("processCode", processCode);
        }
    }


}
