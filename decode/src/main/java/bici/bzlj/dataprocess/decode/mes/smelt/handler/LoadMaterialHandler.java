package bici.bzlj.dataprocess.decode.mes.smelt.handler;

import bici.bzlj.dataprocess.core.annotation.MessageHandler;
import bici.bzlj.dataprocess.core.event.MessageEvent;
import bici.bzlj.dataprocess.core.utils.JsonUtils;
import bici.bzlj.dataprocess.decode.common.ForwardHandler;
import bici.bzlj.dataprocess.decode.mes.smelt.entity.LoadMaterialEntity;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.stream.function.StreamBridge;

import java.util.Map;
import java.util.Objects;


/**
 * TODO
 *  原料消耗实绩电文
 * <AUTHOR>
 * @date 2025/5/15 16:17
 */
@MessageHandler(messageType = {}, desc = "原料消耗实绩电文(DXMTM2,DYMTN3)")
@Slf4j
public class LoadMaterialHandler extends ForwardHandler<String> {

    private static final String SERVICE_ID = "load_material";

    @Override
    public String getGyckServiceId() {
        return SERVICE_ID;
    }

    protected LoadMaterialHandler(StreamBridge streamBridge) {
        super(streamBridge);
    }

    /**
     * 消息处理
     *
     * @param event         消息事件
     * @param currentHandleContext 处理上下文
     */
    @Override
    public void handle(MessageEvent<String> event, Map<String, Object> currentHandleContext, Map<String, Object> handlesContext) {
        ArrayNode loadMaterialNodes = (ArrayNode)JsonUtils.toJsonNode(Objects.requireNonNull(event.getPayload())).get("LOOP");
        ObjectMapper mapper = new ObjectMapper();
        ArrayNode nodes = mapper.createArrayNode();
        loadMaterialNodes.iterator().forEachRemaining(loadMaterialNode -> {
            LoadMaterialEntity loadMaterial = JsonUtils.fromJson(loadMaterialNode.toString(), LoadMaterialEntity.class);
            ObjectNode node = mapper.createObjectNode();
            node.put("taskCode", loadMaterial.getPlanNo());
            node.put("heatNumber", loadMaterial.getHeatNo());
            node.put("bitchNo", loadMaterial.getRawBatchNo());
            node.put("weight", loadMaterial.getMatWt());
            node.put("materialName", StringUtils.isEmpty(loadMaterial.getMaterialName()) ? loadMaterial.getMaterialCode() : loadMaterial.getMaterialName());
            node.put("materialCode", loadMaterial.getMaterialCode());
            node.put("yardPlaceNo", loadMaterial.getYardPlaceNo());
            nodes.add(node);
        });
        currentHandleContext.put("serviceId", SERVICE_ID);
        currentHandleContext.put(SERVICE_ID, JsonUtils.toJson(nodes));
    }

}
