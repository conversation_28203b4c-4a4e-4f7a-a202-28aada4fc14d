package bici.bzlj.dataprocess.decode.unifiedauthorization.user.handler;

import bici.bzlj.dataprocess.core.annotation.MessageHandler;
import bici.bzlj.dataprocess.core.event.MessageEvent;
import bici.bzlj.dataprocess.decode.unifiedauthorization.common.entity.Block;
import bici.bzlj.dataprocess.decode.unifiedauthorization.common.handler.AbsUnifiedAuthorizationHandler;
import bici.bzlj.dataprocess.decode.unifiedauthorization.user.entity.SyncUserUserGroupImportEntity;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.stream.function.StreamBridge;

import java.util.List;
import java.util.Map;

/**
 * 用户用户组关系导入同步消息
 *
 * <AUTHOR>
 * @date 2025/5/15 16:17
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@MessageHandler(messageType = "R_BE_ES_26", desc = "用户用户组关系导入同步消息(R_BE_ES_26)")
@Slf4j
public class SyncUserUserGroupImportHandler extends AbsUnifiedAuthorizationHandler<SyncUserUserGroupImportEntity> {
    protected SyncUserUserGroupImportHandler(StreamBridge streamBridge) {
        super(streamBridge);
    }

    /**
     * 消息处理
     *
     * @param event                消息事件
     * @param currentHandleContext 处理上下文
     */
    @Override
    public void handle(MessageEvent<SyncUserUserGroupImportEntity> event,
                       Map<String, Object> currentHandleContext,
                       Map<String, Object> handlesContext) {
        SyncUserUserGroupImportEntity payload = event.getPayload();


        Block table1 = payload.getTable1();
        List<JsonNode> table1Nodes = processBlockData(table1);
        // 转换数据格式
        List<Map<String, Object>> insertData = dataConvert(table1Nodes);
        // 构建结果数据 - 将两个list合并成一个数组
        Map<String, Object> result = Map.of("insertData", insertData);
        currentHandleContext.put("forward_topic", "unified_authorization");
        currentHandleContext.put(payload.getTelegramId(), result);
    }

}
