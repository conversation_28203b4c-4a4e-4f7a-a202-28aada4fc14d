package bici.bzlj.dataprocess.decode.mes.smelt.entity;

import bici.bzlj.dataprocess.decode.annotation.ImplantAttr;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 特冶感应炉材料产出实绩电文接收
 * DXMTM1
 */
@Data
public class ProductionPerformanceEntity {

    // 单体部分字段
    /**
     * 制造命令号
     */
    @JsonProperty("PONO")
    private String pono;

    /**
     * 熔炼号（炉号）
     */
    @JsonProperty("HEAT_NO")
    private String heatNo;

    /**
     * 厂别区分（默认11）
     */
    @JsonProperty("FACTORY_DIV")
    @ImplantAttr("operationLog")
    private String factoryDiv;

    /**
     * 产出厂别（默认11）
     */
    @JsonProperty("FACTORY_PROD")
    @ImplantAttr("operationLog")
    private String factoryProd;

    /**
     * 感应冶炼类型
     */
    @JsonProperty("AVIM_MELT_TYPE")
    private String avimMeltType;

    /**
     * 机组设备号
     */
    @JsonProperty("MACHINE_NO")
    private String machineNo;

    /**
     * 开始生产时刻
     */
    @JsonProperty("START_PROD_TIME")
    private String startProdTime;

    /**
     * 炉次开始班别
     */
    @JsonProperty("START_SHIFT_GROUP")
    private String startShiftGroup;

    /**
     * 炉次开始班次
     */
    @JsonProperty("START_SHIFT_NO")
    private String startShiftNo;

    /**
     * 开始生产责任者
     */
    @JsonProperty("START_PROD_MAKER")
    private String startProdMaker;

    /**
     * 结束生产时刻
     */
    @JsonProperty("END_PROD_TIME")
    private String endProdTime;

    /**
     * 炉次结束班别
     */
    @JsonProperty("END_SHIFT_GROUP")
    private String endShiftGroup;

    /**
     * 炉次结束班次
     */
    @JsonProperty("END_SHIFT_NO")
    private String endShiftNo;

    /**
     * 生产结束责任者
     */
    @JsonProperty("END_PROD_MAKER")
    private String endProdMaker;

    /**
     * 出口材料支数
     */
    @JsonProperty("MAT_NUM_EXIT")
    @JsonAlias("MAT_NUM")
    @ImplantAttr("operationLog")
    private Integer matNumExit;

    /**
     * 出口材料重量_1
     */
    @JsonProperty("MAT_WT_EXIT")
    @ImplantAttr("operationLog")
    private BigDecimal matWtExit;

    /**
     * 原料消耗总重
     */
    @JsonProperty("MAT_WT_SUM")
    @ImplantAttr("operationLog")
    private BigDecimal matWtSum;

    /**
     * 冷热标志（实际值，1-热，2-冷）
     */
    @JsonProperty("COLD_HOT_FLAG")
    @ImplantAttr("operationLog")
    private String coldHotFlag;

    /**
     * 计量标志
     */
    @JsonProperty("WEIGHT_FLAG")
    @ImplantAttr("operationLog")
    private String weightFlag;

    /**
     * 电耗
     */
    @JsonProperty("ENERGY_SUPPLIED")
    @ImplantAttr("operationLog")
    private BigDecimal energySupplied;

    /**
     * 材料代码
     */
    @JsonProperty("MATERIAL_CODE")
    @ImplantAttr("operationLog")
    private String materialCode;

    /**
     * 原物料代码
     */
    @JsonProperty("MATERIAL_CODE_OLD")
    @ImplantAttr("operationLog")
    private String materialCodeOld;

    /**
     * 使用次数_YF
     */
    @JsonProperty("USE_NUM")
    @ImplantAttr("operationLog")
    private Integer useNum;

    /**
     * 炉代（感应不为空）
     */
    @JsonProperty("FURNANCE_ERA")
    @ImplantAttr("operationLog")
    private Integer furnanceEra;

    /**
     * 炉龄（感应不为空）
     */
    @JsonProperty("FURNACE_AGE")
    @ImplantAttr("operationLog")
    private Integer furnaceAge;

    // 备用字段
    @JsonProperty("BACK_C1")
    private String backC1;
    @JsonProperty("BACK_C2")
    private String backC2;
    @JsonProperty("BACK_C3")
    private String backC3;
    @JsonProperty("BACK_C4")
    private String backC4;
    @JsonProperty("BACK_C5")
    private String backC5;

    // 循环部分（List集合）
    @JsonProperty("LOOP")
    private List<Loop> loops;

    @Data
    public static class Loop {
        /**
         * 材料号
         */
        @JsonProperty("MAT_NO")
        private String matNo;

        /**
         * 定制材料号
         */
        @JsonProperty("CUST_MAT_NO")
        @ImplantAttr("specificationAttr")
        private String custMatNo;

        /**
         * 实绩类型（0正常，2-余材产出）
         */
        @JsonProperty("ACT_TYPE")
        @ImplantAttr("specificationAttr")
        private String actType;

        /**
         * 锭坯型
         */
        @JsonProperty("PROFILE_ID")
        @ImplantAttr(value = "specificationAttr", alias = "ingotType")
        private String profileId;

        /**bat
         * 结晶器号（新增）
         */
        @JsonProperty("CRYSTAL_NO")
        @ImplantAttr("operationLog")
        private String crystalNo;

        /**
         * 材料实际厚度
         */
        @JsonProperty("MAT_ACT_THICK")
        @ImplantAttr(value = "physicalAttr",alias = "thick")
        private BigDecimal matActThick;

        /**
         * 材料实际宽度
         */
        @JsonProperty("MAT_ACT_WIDTH")
        @ImplantAttr(value = "physicalAttr",alias = "width")
        private BigDecimal matActWidth;

        /**
         * 材料实际长度
         */
        @JsonProperty("MAT_ACT_LEN")
        @ImplantAttr(value = "physicalAttr",alias = "length")
        private BigDecimal matActLen;

        /**
         * 材料实际重量
         */
        @JsonProperty("MAT_ACT_WT")
        @ImplantAttr(value = "physicalAttr",alias = "weight")
        private BigDecimal matActWt;

        /**
         * 材料理论重量
         */
        @JsonProperty("MAT_THEORY_WT")
        @ImplantAttr(value = "physicalAttr",alias = "theoryWeight")
        private BigDecimal matTheoryWt;

        /**
         * 称重标记（0-未称重，1-称重）
         */
        @JsonProperty("MEASURE_WT_FLAG")
        @ImplantAttr(value = "physicalAttr",alias = "MEASURE_WT_FLAG")
        private String measureWtFlag;

        /**
         * 生产结束标记（整个计划结束标记，1-结束，0-未结束）
         */
        @JsonProperty("PROD_END_FLAG")
        @ImplantAttr("specificationAttr")
        private String prodEndFlag;

        /**
         * 入口材料号
         */
        @JsonProperty("IN_MAT_NO")
        private String inMatNo;

        /**
         * 合同号
         */
        @JsonProperty("ORDER_NO")
        @ImplantAttr("specificationAttr")
        private String orderNo;

        /**
         * 封锁标记（0-不封锁，2-封锁）
         */
        @JsonProperty("HOLD_FLAG")
        @ImplantAttr("specificationAttr")
        private String holdFlag;

        /**
         * 封锁原因代码
         */
        @JsonProperty("HOLD_CAUSE_CODE")
        @ImplantAttr("specificationAttr")
        private String holdCauseCode;

        /**
         * 封锁时刻
         */
        @JsonProperty("HOLD_TIME")
        @ImplantAttr("specificationAttr")
        private String holdTime;

        /**
         * 封锁责任者
         */
        @JsonProperty("HOLD_MAKER")
        @ImplantAttr("specificationAttr")
        private String holdMaker;

        /**
         * 封锁注释
         */
        @JsonProperty("HOLD_REMARK")
        @ImplantAttr("specificationAttr")
        private String holdRemark;

        /**
         * 表面判定代码（默认1(1-合格，2-不合格)）
         */
        @JsonProperty("SURFACE_DECIDE_CODE")
        @ImplantAttr("specificationAttr")
        private String surfaceDecideCode;

        /**
         * 表面判定时间
         */
        @JsonProperty("SURFACE_DECIDE_TIME")
        @ImplantAttr("specificationAttr")
        private String surfaceDecideTime;

        /**
         * 表面判定责任者
         */
        @JsonProperty("SURFACE_DECIDE_MAKER")
        @ImplantAttr("specificationAttr")
        private String surfaceDecideMaker;

        /**
         * 缺陷代码
         */
        @JsonProperty("DEFECT_CODE")
        @ImplantAttr("specificationAttr")
        private String defectCode;
    }
}