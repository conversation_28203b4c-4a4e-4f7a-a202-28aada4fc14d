spring:
  cloud:
    stream:
      kafka:
        binder:
          brokers: ${KAFKA:10.81.12.47:9092,10.81.12.48:9092,10.81.12.49:9092}
          auto-create-topics: true   # 自动创建topics
          required-acks: -1 #0：这意味着生产者producer不等待来自broker同步完成的确认继续发送下一条（批）消息。此选项提供最低的延迟但最弱的耐久性保证（当服务器发生故障时某些数据会丢失，如leader已死，但producer并不知情，发出去的信息broker就收不到）。
          #          # 1：这意味着producer在leader已成功收到的数据并得到确认后发送下一条message。此选项提供了更好的耐久性为客户等待服务器确认请求成功（被写入死亡leader但尚未复制将失去了唯一的消息）。
          #          #-1：这意味着producer在follower副本确认接收到数据后才算一次发送完成。 此选项提供最好的耐久性，我们保证没有信息将丢失，只要至少一个同步副本保持存活
          transaction:
            producer:
              buffer-size: 16384 #Kafka 生产者在发送之前尝试批处理的数据量的上限（以字节为单位）。
              batch-timeout: 100 #生产者在发送消息之前等待多长时间以允许在同一批次中累积更多消息,每批消息创建后最多等待多少毫秒必须发送出去，不管本批次装没装满
      binding-retry-interval: 30 #绑定重试间隔，默认30秒
      bindings:
        unifiedAuthorizationConsumer-in-0: # 消费者topic配置，bindingName命名规范：bean名称-in-索引，索引值为参数个数
          destination: bwty_unified_authorization #相当于topic(需要根据实际情况修改topic,这里只是一个示例)
          group: group_decode #相当于group.id
          consumer:
            concurrency: 3 #消费并发数量,默认值1
            batchMode: true # 默认false,是否批量消费，false的话就会一条一条消费，如果是true的话在bean配置里面的参数应该是List<>
        meltingChemicalCompositionConsumer-in-0: # 消费者topic配置，bindingName命名规范：bean名称-in-索引，索引值为参数个数
          destination: bwty_melting_chemical_composition #相当于topic(需要根据实际情况修改topic,这里只是一个示例)
          group: group_decode #相当于group.id
          consumer:
            concurrency: 3 #消费并发数量,默认值1
            batchMode: true # 默认false,是否批量消费，false的话就会一条一条消费，如果是true的话在bean配置里面的参数应该是List<>
        mesSmeltConsumer-in-0: # 消费者topic配置，bindingName命名规范：bean名称-in-索引，索引值为参数个数
          destination: bwty_mes_smelt #相当于topic(需要根据实际情况修改topic,这里只是一个示例)
          group: group_decode #相当于group.id
          consumer:
            concurrency: 3 #消费并发数量,默认值1
            batchMode: true # 默认false,是否批量消费，false的话就会一条一条消费，如果是true的话在bean配置里面的参数应该是List<>
        mesForgeConsumer-in-0: # 消费者topic配置，bindingName命名规范：bean名称-in-索引，索引值为参数个数
          destination: bwty_mes_forge #相当于topic(需要根据实际情况修改topic,这里只是一个示例)
          group: group_decode #相当于group.id
          consumer:
            concurrency: 3 #消费并发数量,默认值1
            batchMode: true # 默认false,是否批量消费，false的话就会一条一条消费，如果是true的话在bean配置里面的参数应该是List<>
        mesPipeConsumer-in-0: # 消费者topic配置，bindingName命名规范：bean名称-in-索引，索引值为参数个数
          destination: bwty_mes_pipe #相当于topic(需要根据实际情况修改topic,这里只是一个示例)
          group: group_decode #相当于group.id
          consumer:
            concurrency: 3 #消费并发数量,默认值1
            batchMode: true # 默认false,是否批量消费，false的话就会一条一条消费，如果是true的话在bean配置里面的参数应该是List<>
        mesInspectionConsumer-in-0: # 消费者topic配置，bindingName命名规范：bean名称-in-索引，索引值为参数个数
          destination: bwty_mes_inspection #相当于topic(需要根据实际情况修改topic,这里只是一个示例)
          group: group_decode #相当于group.id
          consumer:
            concurrency: 3 #消费并发数量,默认值1
            batchMode: true # 默认false,是否批量消费，false的话就会一条一条消费，如果是true的话在bean配置里面的参数应该是List<>
        mesTitaniumSmeltConsumer-in-0: # 消费者topic配置，bindingName命名规范：bean名称-in-索引，索引值为参数个数
          destination: bwty_mes_titanium_smelt #相当于topic(需要根据实际情况修改topic,这里只是一个示例)
          group: group_decode #相当于group.id
          consumer:
            concurrency: 3 #消费并发数量,默认值1
            batchMode: true # 默认false,是否批量消费，false的话就会一条一条消费，如果是true的话在bean配置里面的参数应该是List<>
        mesTitaniumIsothermalForgingConsumer-in-0: # 消费者topic配置，bindingName命名规范：bean名称-in-索引，索引值为参数个数
          destination: bwty_mes_titanium_isothermal_forging #相当于topic(需要根据实际情况修改topic,这里只是一个示例)
          group: group_decode #相当于group.id
          consumer:
            concurrency: 3 #消费并发数量,默认值1
            batchMode: true # 默认false,是否批量消费，false的话就会一条一条消费，如果是true的话在bean配置里面的参数应该是List<>
    function:
      definition: unifiedAuthorizationConsumer;meltingChemicalCompositionConsumer;mesSmeltConsumer;mesForgeConsumer;mesPipeConsumer;mesInspectionConsumer;mesTitaniumSmeltConsumer;mesTitaniumIsothermalForgingConsumer;
  kafka:
    producer:
      retries: 3 #消息发送失败后，重试次数
      batch-size: 16384
      buffer-memory: 33554432 #kafka内存缓冲区大小 33554432-32M，134217728-128M 默认32MB
      acks: -1
    consumer:
      enable-auto-commit: true #消费者的偏移量是否在后台定期提交。
      auto-offset-reset: earliest #当Kafka中没有初始偏移量或服务器上不再存在当前偏移量时该怎么办。latest自动将offset偏移重置为最新偏移，从此偏移开始消费
      #      fetch-max-wait: 100ms
      #      fetch-min-size: 1
      max-poll-records: 1000 # 消费者一次拉取数据的最大条数

disruptor:
  buffer:
    size: 1048576
  thread:
    max: 4
  wait:
    strategy: YIELDING
dynamic:
  mongo:
    enable-dynamic: true
    primary: primary
    datasource:
      primary:
        name: primary
        url: ${DECODE_MONGO_URL:10.81.12.53:27017,10.81.12.54:27017,10.81.12.55:27017}
        database: ${DECODE_MONGO_DATABASE:bwty-craft-pro}
        username: ${DECODE_MONGO_USERNAME:admin}
        password: ${DECODE_MONGO_PWD:qbUgBCCCKZ47pI7i}
        authentication-database: admin