<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>bici.bzlj</groupId>
    <artifactId>data-process-engine</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>
    <modules>
        <module>core</module>
        <module>decode</module>
    </modules>
    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <java.version>21</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <dynamic.mongo.version>1.0.1</dynamic.mongo.version>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.bzlj</groupId>
                <artifactId>bzlj-framework-dependencies</artifactId>
                <version>1.0.3</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <repositories>
        <repository>
            <id>bici</id>
            <name>bici nexus</name>
            <url>https://nexus.bicisims.com/repository/maven-public/</url>
        </repository>
    </repositories>
    <pluginRepositories>
        <pluginRepository>
            <id>bici</id>
            <name>bici nexus</name>
            <url>https://nexus.bicisims.com/repository/maven-public/</url>
        </pluginRepository>
    </pluginRepositories>
    <distributionManagement>
        <repository>
            <id>bici</id>
            <name>bici nexus</name>
            <url>https://nexus.bicisims.com/repository/maven-releases/</url>
        </repository>
    </distributionManagement>
</project>